import Link from 'next/link';
import { useEffect, useState, useRef } from 'react';
import pathName from '@/constants/pathName';
import { useAuth } from '@/context/AuthContext';
import { createClient } from '@/utils/supabase/client';
import HeaderSelectWorkspace from './HeaderSelectWorkspace';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowLeft, faBars, faBuilding, faUser, faSignOutAlt } from "@fortawesome/free-solid-svg-icons";
import { useParams, usePathname } from 'next/navigation';
import HeaderNavMobile from './HeaderNavMobile';

export default function HeaderBurger() {
    const { user, isLoadingUserData } = useAuth();
    const path = usePathname();
    const isWorkspace = path?.includes(pathName.workspace) && path?.split('/').length >= 3;
    const dropdownRef = useRef<HTMLDivElement>(null);
    const [dropdownOpen, setDropdownOpen] = useState(false);

    
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setDropdownOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleLogout = async () => {
        const { error } = await createClient().auth.signOut();
        if (error) {
          console.log('Error logout:', error.message);
        } else {
          window.location.href = '/';
        }
    };
    
    if(isLoadingUserData) {
        return <></>;
    }


    return (
        <div className="flex items-center space-x-4 relative z-10">
            {isWorkspace ? 
                <Link href={'/'} className="cursor-pointer shadow-sm w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors" aria-label="Back to workspace selection">
                    <FontAwesomeIcon icon={faArrowLeft} className="h-3 w-3 text-gray-600" />
                </Link> 
            : null}
            {isWorkspace ? <HeaderSelectWorkspace /> : null}
            {
                user ? (
                    <div className="relative" ref={dropdownRef}>
                <div 
                    className="flex items-center space-x-3 group cursor-pointer" 
                    onClick={() => setDropdownOpen(!dropdownOpen)}
                >
                    <div className="h-8 w-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 transition-all ring-2 ring-transparent group-hover:ring-gray-300">
                        <FontAwesomeIcon icon={faBars} className="h-4 w-4 text-black" />
                    </div>
                </div>
                
                {dropdownOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                         <HeaderNavMobile />
                         
                        <Link 
                            href={'/'}
                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                            onClick={() => setDropdownOpen(false)}
                        >
                            <FontAwesomeIcon icon={faBuilding} className="mr-2 h-4 w-4 text-gray-500" />
                            Workspaces
                        </Link>
                        
                        <Link 
                            href={pathName.profile}
                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                            onClick={() => setDropdownOpen(false)}
                        >
                            <FontAwesomeIcon icon={faUser} className="mr-2 h-4 w-4 text-gray-500" />
                            Profile
                        </Link>
                        
                        <button 
                            onClick={() => {
                                setDropdownOpen(false);
                                handleLogout();
                            }}
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                        >
                            <FontAwesomeIcon icon={faSignOutAlt} className="mr-2 h-4 w-4 text-gray-500" />
                            Sign out
                        </button>
                    </div>
                )}
            </div>
                ) : (
                    <div>
                        <Link href={pathName.login} className="px-4 py-2 text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-200">Sign in</Link>
                    </div>
                )
            }
        </div>
    )
}