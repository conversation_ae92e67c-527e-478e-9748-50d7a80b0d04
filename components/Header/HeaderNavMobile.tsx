import { useParams, usePathname } from "next/navigation";
import Link from 'next/link';
import pathName from "@/constants/pathName";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faHome, faShare, faSearch, faList, faBriefcase, faMoneyBill } from "@fortawesome/free-solid-svg-icons";

export default function HeaderNavMobile() {
    const path = usePathname();
    const isWorkspace = path.includes(pathName.workspace) && path.split('/').length >= 3;
    const params = useParams();
    const workspaceId = params.id;

    return (
        <div className='lg:hidden'>
            {
                path === '/' || path === pathName.pricing || path === pathName.workspace || path === pathName.shared || path.includes(pathName.portfolio) ?
                    <div>
                        <div>
                            <Link href={'/'} className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center '>
                                <FontAwesomeIcon icon={faHome} className="mr-2 h-4 w-4 text-gray-500" />
                                Home
                            </Link>
                        </div>
                        <div>
                            <Link href={`${pathName.shared}`} className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center'>
                                <FontAwesomeIcon icon={faShare} className="mr-2 h-4 w-4 text-gray-500" />
                                Shared
                            </Link>
                        </div>
                    </div>
                : null
            }

            {
                isWorkspace ? 
                    <div>
                        <div>
                            <Link href={`/workspace/${workspaceId}${pathName.search}`} className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center'>
                                <FontAwesomeIcon icon={faSearch} className="mr-2 h-4 w-4 text-gray-500" />
                            Property Search
                            </Link>
                        </div>
                        <div>
                            <Link href={`/workspace/${workspaceId}`} className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center'>
                                <FontAwesomeIcon icon={faList} className="mr-2 h-4 w-4 text-gray-500" />
                            Portfolios
                            </Link>
                        </div>
                    
                        <div>
                            <Link href={`/workspace/${workspaceId}/market-brief`} className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center'>
                                <FontAwesomeIcon icon={faBriefcase} className="mr-2 h-4 w-4 text-gray-500" />
                            Market Brief
                            </Link>
                        </div>
                        <div>
                            <Link href={`/workspace/${workspaceId}${pathName.pricing}`} className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center'>
                                <FontAwesomeIcon icon={faMoneyBill} className="mr-2 h-4 w-4 text-gray-500" />
                            Data Access
                            </Link>
                        </div>
                    </div>
                : null
            }
        </div>
    )
}