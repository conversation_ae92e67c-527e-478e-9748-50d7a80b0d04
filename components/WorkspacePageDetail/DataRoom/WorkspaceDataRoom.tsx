'use client'
import { getPortfolioDocuments, getPortfolioDocumentsTest, toggleDocumentHidden } from '@/actions/dataRoomActions';
import { useParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import DocumentTree from '@/components/WorkspacePageDetail/DataRoom/DocumentTree';
import PDFViewer from '@/components/WorkspacePageDetail/DataRoom/PDFViewer';
import { usePortfolio } from '@/context/PortfolioContext';
import { useModal } from '@/context/ModalContext';
import modalTriggerType from '@/constants/modalTriggerType';
import Spinner from '@/components/UI/Spinner';
import modalType from '@/constants/modalType';
import { useAuth } from '@/context/AuthContext';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExpand, faCompress, faTimes, faChevronUp, faFileLines } from '@fortawesome/free-solid-svg-icons';

interface DocumentNode {
  id: string;
  type: 'folder' | 'file';
  name: string;
  document_type?: string;
  children?: DocumentNode[];
  url?: string;
  created_at?: string;
  uploaded_at?: string;
  summary?: string;
  page_count?: number;
  is_hidden?: boolean;
}

interface WorkspaceDataRoomProps {
  isChatExpanded?: boolean;
}

export default function WorkspaceDataRoom({ isChatExpanded = false }: WorkspaceDataRoomProps) {
  const {user} = useAuth();
  const [documents, setDocuments] = useState<any>(null);
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<DocumentNode | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [collapseAllTrigger, setCollapseAllTrigger] = useState(0);
  const { selectedPortfolio } = usePortfolio();
  const {showModal, updateModalData, updateModalTrigger, modalTrigger} = useModal();
  
  // Since this is workspace view, user is always the owner
  const isOwner = true;

  // Function to recursively find offering memorandum
  const findOfferingMemorandum = (node: DocumentNode): DocumentNode | null => {
    if (node.type === 'file' && node.document_type === 'offering_memorandum') {
      return node;
    }
    
    if (node.children) {
      for (const child of node.children) {
        const found = findOfferingMemorandum(child);
        if (found) return found;
      }
    }
    
    return null;
  };

  // Function to recursively find a document by URL
  const findDocumentByUrl = (node: DocumentNode, url: string): DocumentNode | null => {
    if (node.type === 'file' && node.url === url) {
      return node;
    }
    
    if (node.children) {
      for (const child of node.children) {
        const found = findDocumentByUrl(child, url);
        if (found) return found;
      }
    }
    
    return null;
  };

  useEffect(() => {
    getPortfolioDocuments(selectedPortfolio?.id as string).then((res) => {
      setDocuments(res);
      
      // Automatically select offering memorandum if it exists
      if (res) {
        const offeringMemo = findOfferingMemorandum(res);
        if (offeringMemo && offeringMemo.url) {
          setSelectedFile(offeringMemo.url);
          setSelectedDocument(offeringMemo);
        }
      }
    });
  }, [selectedPortfolio]);

  useEffect(() => {
    if (modalTrigger === modalTriggerType.deletePortfolioDocument) {
      getPortfolioDocuments(selectedPortfolio?.id as string).then((res) => {
        setDocuments(res);
        
        // Re-select offering memorandum after document operations
        if (res) {
          const offeringMemo = findOfferingMemorandum(res);
          if (offeringMemo && offeringMemo.url) {
            setSelectedFile(offeringMemo.url);
            setSelectedDocument(offeringMemo);
          }
        }
      });
      updateModalTrigger(null);
    }
  }, [modalTrigger]);

  const handleFileSelect = (url: string) => {
    setSelectedFile(url);
    
    // Find and set the selected document to get page count
    if (documents) {
      const document = findDocumentByUrl(documents, url);
      console.log('Selected document:', document);
      console.log('Document page_count:', document?.page_count);
      setSelectedDocument(document);
    }
  };

  const handleFullscreenToggle = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleCloseFullscreen = () => {
    setIsFullscreen(false);
  };

  // Handle escape key to close fullscreen
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleEscapeKey);
      // Prevent body scroll when in fullscreen
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'unset';
    };
  }, [isFullscreen]);

  const handleCollapseAll = () => {
    setCollapseAllTrigger(prev => prev + 1);
  };

  // Added document refresh and hide handlers
  const refreshDocuments = async () => {
    const res = await getPortfolioDocuments(selectedPortfolio?.id as string);
    setDocuments(res);
    
    // Keep the same file selected if it still exists
    if (selectedFile && res) {
      const document = findDocumentByUrl(res, selectedFile);
      setSelectedDocument(document);
    }
    
    return res;
  };

  const handleDocumentHide = async () => {
    if (selectedDocument && selectedPortfolio?.id) {
      try {
        await toggleDocumentHidden(selectedPortfolio.id, selectedDocument.id);
        await refreshDocuments();
      } catch (error) {
        console.error('Error toggling document visibility:', error);
      }
    }
  };

  return (
    <div className="h-full">
      {documents ? (
        <div className="h-full">
          <div className='flex gap-5 h-[calc(100vh-300px)]'>
            <div className="min-w-[300px] w-2/7 rounded-lg overflow-y-auto">
              {/* Collapse All Button */}
              <div className="flex justify-end mb-2 px-2">
                <button
                  onClick={handleCollapseAll}
                  className="cursor-pointer flex items-center gap-2 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                  title="Collapse all folders"
                >
                  
                  Collapse All
                </button>
              </div>
              
              {documents && documents.children ? (
                // If root has children, render them directly to avoid showing root folder icon
                <div className="space-y-0">
                  {documents.children.map((child: DocumentNode, index: number) => (
                    <DocumentTree 
                      key={child.id} 
                      data={child} 
                      level={0}
                      isLast={index === documents.children.length - 1}
                      onFileSelect={handleFileSelect} 
                      portfolioId={selectedPortfolio?.id as string} 
                      selectedFile={selectedFile}
                      isSharedView={false}
                      showUploadButton={index === documents.children.length - 1}
                      collapseAllTrigger={collapseAllTrigger}
                      isOwner={isOwner}
                      onDocumentUpdate={refreshDocuments}
                    />
                  ))}
                </div>
              ) : (
                // Fallback to normal rendering
                <DocumentTree 
                  data={documents} 
                  onFileSelect={handleFileSelect} 
                  portfolioId={selectedPortfolio?.id as string} 
                  selectedFile={selectedFile}
                  isSharedView={false}
                  showUploadButton={true}
                  collapseAllTrigger={collapseAllTrigger}
                  isOwner={isOwner}
                  onDocumentUpdate={refreshDocuments}
                />
              )}
            </div>
            <div className="flex-1 shadow-lg rounded-[20px] relative">
              {selectedFile ? (
                <>
                  <PDFViewer 
                    url={selectedFile} 
                    pageCount={selectedDocument?.page_count}
                    onFullscreenToggle={handleFullscreenToggle}
                    onClose={handleCloseFullscreen}
                    isFullscreen={isFullscreen}
                    isOwner={isOwner}
                    isHidden={selectedDocument?.is_hidden}
                    onHideToggle={handleDocumentHide}
                    documentName={selectedDocument?.name}
                    documentType={selectedDocument?.document_type}
                  />
                </>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <FontAwesomeIcon icon={faFileLines} className="h-16 w-16 mb-4 text-gray-300" />
                    <h3 className="text-lg font-medium mb-2">No document selected</h3>
                    <p className="text-sm">Choose a document from the left to view it here</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-8">
          <Spinner size="lg" text="Loading documents..." fullPage={false} />
        </div>
      )}

      {/* Fullscreen PDF Modal - Responsive to chat state */}
      {isFullscreen && selectedFile && (
        <div className="fixed inset-0 z-40 bg-white flex flex-col">
          {/* Fullscreen Header */}
          <div className="flex items-center justify-between p-4 bg-gray-50 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <h3 className="text-gray-900 font-medium text-lg">
                {selectedDocument?.name || 'Document Viewer'}
              </h3>
              {selectedDocument?.page_count && (
                <span className="text-gray-600 text-sm">
                  {selectedDocument.page_count} pages
                </span>
              )}
            </div>
          </div>

          {/* Fullscreen Content - Adjust layout based on chat state */}
          <div className="flex-1 p-4 transition-all duration-300"
               style={{
                 // If chat is expanded, leave space for it on the right
                 marginRight: isChatExpanded ? '400px' : '0'
               }}>
            <div className="w-full h-full bg-white rounded-lg shadow-lg">
              <PDFViewer 
                url={selectedFile} 
                pageCount={selectedDocument?.page_count}
                onFullscreenToggle={handleFullscreenToggle}
                onClose={handleCloseFullscreen}
                isFullscreen={true}
                isOwner={isOwner}
                isHidden={selectedDocument?.is_hidden}
                onHideToggle={handleDocumentHide}
                documentName={selectedDocument?.name}
                documentType={selectedDocument?.document_type}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 