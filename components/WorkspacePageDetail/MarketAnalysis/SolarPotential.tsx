import { useMarketAnalysis } from "@/context/MarketAnalysisContext"
import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import { createClient } from "@/utils/supabase/client";
import Spinner from "@/components/UI/Spinner";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSun, faLeaf, faRotate, faExclamationTriangle, faCircleInfo } from "@fortawesome/free-solid-svg-icons";

export default function SolarPotential() {
    const {searchPlace, updateMarketAnalysisState, solarData, isLoadingSolarData} = useMarketAnalysis()
    const searchParams = useSearchParams()
    const addressId = searchParams.get('addressId')
    const [isRefreshing, setIsRefreshing] = useState<boolean>(false)
    const [error, setError] = useState<string | null>(null)


    useEffect(() =>{
        if(typeof searchPlace !== 'string'){
            fetch(`/api/solar-potential?lat=${searchPlace.geometry.location.lat()}&lng=${searchPlace.geometry.location.lng()}`)
            .then(res => res.json())
            .then(data => {
                updateMarketAnalysisState({solarData: data?.parsedData, isLoadingSolarData: false})
            })
        }
    }, [searchPlace])

    const handleRefresh = async () => {
        try {
            setIsRefreshing(true)
            setError(null)
            updateMarketAnalysisState({isLoadingSolarData: true})
            
            const supabase = createClient();
            const {data: propAddressData, error: propAddressError} = await supabase
                .from('prop_addresses')
                .select('*')
                .eq('id', addressId as string)
                .single();
                
            if (propAddressError) throw new Error('Failed to fetch property address data')
            
            const {data: propData, error: propError} = await supabase
                .from('prop')
                .select('*')
                .eq('address_id', addressId as string)
                .single();
                
            if (propError) throw new Error('Failed to fetch property data')
            
            try {
                const response = await fetch(`/api/solar?lat=${propAddressData?.lat}&lng=${propAddressData?.lon}`)
                const solar = await response.json()
                
                if (!solar?.parsedData) {
                    throw new Error('No solar data available for this location')
                }
                
                updateMarketAnalysisState({solarData: solar.parsedData, isLoadingSolarData: false})

                await supabase
                    .from('prop_solar_potential')
                    .update({ data: solar.parsedData })
                    .eq('prop_id', propData.id)
            } catch (solarError) {
                if (solarError instanceof Error) {
                    if (solarError.message.includes('401') || solarError.message.includes('403')) {
                        throw new Error('Google API authentication error. Please check API key configuration and referrer settings.');
                    } else if (solarError.message.includes('JSON')) {
                        throw new Error('Invalid response from solar API. Please check the API endpoint.');
                    } else {
                        throw solarError;
                    }
                } else {
                    throw solarError;
                }
            }
                
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to refresh solar data')
            updateMarketAnalysisState({isLoadingSolarData: false})
        } finally {
            setIsRefreshing(false)
        }
    }

    const renderLoadingState = () => (
        <div className="animate-pulse">
            <div className="grid grid-cols-2 gap-6">
                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 p-5 rounded-lg shadow-sm">
                    <div className="h-6 w-32 bg-gray-200 rounded mb-4"></div>
                    <div className="grid grid-cols-2 gap-4">
                        {[...Array(4)].map((_, i) => (
                            <div key={i} className="bg-white/50 p-3 rounded-lg">
                                <div className="h-4 w-20 bg-gray-200 rounded mb-2"></div>
                                <div className="h-5 w-24 bg-gray-200 rounded"></div>
                            </div>
                        ))}
                    </div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 p-5 rounded-lg shadow-sm h-[280px]">
                    <div className="h-6 w-40 bg-gray-200 rounded mb-4"></div>
                    <div className="grid grid-cols-2 gap-4">
                        <div className="bg-white/50 p-3 rounded-lg">
                            <div className="h-4 w-28 bg-gray-200 rounded mb-2"></div>
                            <div className="h-5 w-20 bg-gray-200 rounded"></div>
                        </div>
                        <div className="bg-white/50 p-3 rounded-lg">
                            <div className="h-4 w-32 bg-gray-200 rounded mb-2"></div>
                            <div className="h-5 w-24 bg-gray-200 rounded"></div>
                        </div>
                        <div className="col-span-2 bg-white/50 p-3 rounded-lg">
                            <div className="h-4 w-24 bg-gray-200 rounded mb-2"></div>
                            <div className="h-5 w-36 bg-gray-200 rounded"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )

    const renderErrorState = () => (
        <div className="flex flex-col items-center justify-center py-8 text-center">
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-yellow-500 text-3xl mb-3" />
            <p className="text-gray-700 font-medium mb-2">Unable to load solar data</p>
            <p className="text-sm text-gray-500 mb-4">{error}</p>
            {addressId && (
                <button 
                    onClick={handleRefresh}
                    className="inline-flex items-center gap-x-2 px-4 py-2 text-sm font-medium text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100 transition-colors">
                    <FontAwesomeIcon icon={faRotate} className="h-4 w-4" />
                    Try Again
                </button>
            )}
        </div>
    )

    const renderEmptyState = () => (
        <div className="animate-pulse">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 p-5 rounded-lg shadow-sm">
                    <div className="h-6 w-32 bg-gray-200 rounded mb-4"></div>
                    <div className="grid grid-cols-2 gap-4">
                        {[...Array(4)].map((_, i) => (
                            <div key={i} className="bg-white/50 p-3 rounded-lg">
                                <div className="h-4 w-20 bg-gray-200 rounded mb-2"></div>
                                <div className="h-5 w-24 bg-gray-200 rounded"></div>
                            </div>
                        ))}
                    </div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 p-5 rounded-lg shadow-sm">
                    <div className="h-6 w-40 bg-gray-200 rounded mb-4"></div>
                    <div className="grid grid-cols-2 gap-4">
                        <div className="bg-white/50 p-3 rounded-lg">
                            <div className="h-4 w-28 bg-gray-200 rounded mb-2"></div>
                            <div className="h-5 w-20 bg-gray-200 rounded"></div>
                        </div>
                        <div className="bg-white/50 p-3 rounded-lg">
                            <div className="h-4 w-32 bg-gray-200 rounded mb-2"></div>
                            <div className="h-5 w-24 bg-gray-200 rounded"></div>
                        </div>
                        <div className="col-span-2 bg-white/50 p-3 rounded-lg">
                            <div className="h-4 w-24 bg-gray-200 rounded mb-2"></div>
                            <div className="h-5 w-36 bg-gray-200 rounded"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )

    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {/* Card Header */}
            <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <FontAwesomeIcon icon={faSun} className="h-4 w-4 text-yellow-600" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800">Solar Potential</h3>
                    </div>
                    {addressId && !isLoadingSolarData && solarData && (
                        <button 
                            onClick={handleRefresh}
                            disabled={isRefreshing}
                            className="cursor-pointer rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline-indigo-600 disabled:opacity-50">
                            {isRefreshing ? (
                                <div className="flex items-center gap-x-1">
                                    <Spinner size="sm" />
                                    Refreshing...
                                </div>
                            ) : 'Refresh'}
                        </button>
                    )}
                </div>
            </div>

            {/* Card Content */}
            <div className="p-6">
                {isLoadingSolarData ? renderLoadingState() : 
                 error ? renderErrorState() :
                 !solarData ? renderEmptyState() : (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 p-5 rounded-lg shadow-sm">
                            <div className="flex items-center gap-x-2 mb-4">
                                <h4 className="text-base font-semibold text-gray-800 flex items-center gap-x-2">
                                    <FontAwesomeIcon icon={faSun} className="text-yellow-500 h-4 w-4" />
                                    System Details
                                </h4>
                                <div className="relative">
                                    <div className="group">
                                        <FontAwesomeIcon icon={faCircleInfo} className="text-gray-400 h-4 w-4" />
                                        <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 w-64 p-2 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity z-10 pointer-events-none">
                                            <p className="mb-1 font-medium">System Details</p>
                                            <p className="text-gray-300">Key metrics that help you understand the property's energy independence potential. A larger system with higher annual production means lower utility bills and potential income from excess energy sales. This directly impacts your property's operating costs and market value.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="bg-white/50 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 mb-1">System Size</p>
                                    <p className="font-semibold text-gray-800">{solarData.systemSize}</p>
                                </div>
                                <div className="bg-white/50 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 mb-1">Panels</p>
                                    <p className="font-semibold text-gray-800">{solarData.panels}</p>
                                </div>
                                <div className="bg-white/50 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 mb-1">Roof Area</p>
                                    <p className="font-semibold text-gray-800">{solarData.roofArea}</p>
                                </div>
                                <div className="bg-white/50 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 mb-1">Annual Production</p>
                                    <p className="font-semibold text-gray-800">{solarData.annualProduction}</p>
                                </div>
                            </div>
                        </div>
                        <div className="bg-gradient-to-br from-green-50 to-green-100 p-5 rounded-lg shadow-sm h-[280px]">
                            <div className="flex items-center gap-x-2 mb-4">
                                <h4 className="text-base font-semibold text-gray-800 flex items-center gap-x-2">
                                    <FontAwesomeIcon icon={faLeaf} className="text-green-500 h-4 w-4" />
                                    Environmental Impact
                                </h4>
                                <div className="relative">
                                    <div className="group">
                                        <FontAwesomeIcon icon={faCircleInfo} className="text-gray-400 h-4 w-4" />
                                        <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 w-64 p-2 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity z-10 pointer-events-none">
                                            <p className="mb-1 font-medium">Environmental Impact</p>
                                            <p className="text-gray-300">Shows how this property can contribute to sustainability goals and attract eco-conscious tenants or buyers. Properties with strong environmental credentials often command premium rents and have higher resale values. These metrics also help qualify for green building incentives and tax benefits.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="bg-white/50 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 mb-1">Annual CO₂ Offset</p>
                                    <p className="font-semibold text-gray-800">{solarData.co2OffsetAnnual}</p>
                                </div>
                                <div className="bg-white/50 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 mb-1">25-Year CO₂ Offset</p>
                                    <p className="font-semibold text-gray-800">{solarData.co2Offset25Year}</p>
                                </div>
                                <div className="col-span-2 bg-white/50 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 mb-1">Equivalent to</p>
                                    <p className="font-semibold text-gray-800">{solarData.trees}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}
