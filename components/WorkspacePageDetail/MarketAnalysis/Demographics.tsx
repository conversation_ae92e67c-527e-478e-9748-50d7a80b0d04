import { fetchDemographics } from "@/actions/propertyActions";
import { useMarketAnalysis } from "@/context/MarketAnalysisContext";
import { createClient } from "@/utils/supabase/client";
import { useSearchParams } from "next/navigation";
import { useState } from "react";
import Spinner from "@/components/UI/Spinner";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUsers, faCircleInfo, faChartLine, faShieldAlt } from "@fortawesome/free-solid-svg-icons";

export default function Demographics() {
    const { demographicsData, isLoadingDemographics, searchPlace, updateMarketAnalysisState } = useMarketAnalysis();
    const searchParams = useSearchParams()
    const addressId = searchParams.get('addressId')
    const [isRefreshing, setIsRefreshing] = useState<boolean>(false)
    const [error, setError] = useState<string | null>(null)

    const handleRefresh = async () => {
        setIsRefreshing(true)
        setError(null)
        updateMarketAnalysisState({ isLoadingDemographics: true })

        try {
            const supabase = createClient();
            const {data: propAddressData, error: propAddressError} = await supabase.from('prop_addresses').select('*').eq('id', addressId as string).single();
            const {data: propData, error: propError} = await supabase.from('prop').select('*').eq('address_id', addressId as string).single();

            if (propAddressError || propError) {
                throw new Error('Error fetching property data')
            }

            const demographicsData = await fetchDemographics(propAddressData?.address as string, propAddressData?.city as string, propAddressData?.state as string, propAddressData?.zip as string)

            updateMarketAnalysisState({ demographicsData, isLoadingDemographics: false })

            const {data: newDemographicsData, error: newDemographicsDataError} = await supabase.from('demographics')
                .update({ data: demographicsData || [] }).eq('prop_id', propData.id);
            
            if (newDemographicsDataError) {
                throw new Error('Error updating demographics data')
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred')
            updateMarketAnalysisState({ isLoadingDemographics: false })
        } finally {
            setIsRefreshing(false)
        }
    }

    const renderLoadingState = () => (
        <div className="animate-pulse">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-5 rounded-lg h-[280px]">
                    <div className="h-6 w-40 bg-gray-200 rounded mb-4"></div>
                    <div className="grid grid-cols-2 gap-4">
                        <div className="bg-white/50 p-3 rounded-lg">
                            <div className="h-4 w-20 bg-gray-200 rounded mb-2"></div>
                            <div className="h-5 w-24 bg-gray-200 rounded"></div>
                        </div>
                        <div className="bg-white/50 p-3 rounded-lg">
                            <div className="h-4 w-24 bg-gray-200 rounded mb-2"></div>
                            <div className="h-5 w-20 bg-gray-200 rounded"></div>
                        </div>
                        <div className="col-span-2 bg-white/50 p-3 rounded-lg">
                            <div className="h-4 w-36 bg-gray-200 rounded mb-2"></div>
                            <div className="h-5 w-28 bg-gray-200 rounded"></div>
                        </div>
                    </div>
                </div>
                <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-5 rounded-lg h-[280px]">
                    <div className="h-6 w-32 bg-gray-200 rounded mb-4"></div>
                    <div className="grid grid-cols-2 gap-4">
                        {[...Array(4)].map((_, i) => (
                            <div key={i} className="bg-white/50 p-3 rounded-lg">
                                <div className="h-4 w-28 bg-gray-200 rounded mb-2"></div>
                                <div className="h-5 w-24 bg-gray-200 rounded"></div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    )

    const renderErrorState = () => (
        <div className="text-red-500">{error}</div>
    )

    const renderEmptyState = () => (
        <div className="text-gray-500">No demographics data found.</div>
    )

    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {/* Card Header */}
            <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                            <FontAwesomeIcon icon={faUsers} className="h-4 w-4 text-purple-600" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800">Demographics</h3>
                    </div>
                    {addressId && !isLoadingDemographics && demographicsData && (
                        <button 
                            onClick={handleRefresh}
                            disabled={isRefreshing}
                            className="cursor-pointer rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline-indigo-600 disabled:opacity-50">
                            {isRefreshing ? (
                                <div className="flex items-center gap-x-1">
                                    <Spinner size="sm" />
                                    Refreshing...
                                </div>
                            ) : 'Refresh'}
                        </button>
                    )}
                </div>
            </div>

            {/* Card Content */}
            <div className="p-6">
                {isLoadingDemographics ? renderLoadingState() : 
                 error ? renderErrorState() :
                 !demographicsData ? renderEmptyState() : (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-5 rounded-lg shadow-sm h-[280px]">
                            <div className="flex items-center gap-x-2 mb-4">
                                <h4 className="text-base font-semibold text-gray-800 flex items-center gap-x-2">
                                    <FontAwesomeIcon icon={faUsers} className="text-blue-500 h-4 w-4" />
                                    Population & Income
                                </h4>
                                <div className="relative">
                                    <div className="group">
                                        <FontAwesomeIcon icon={faCircleInfo} className="text-gray-400 h-4 w-4" />
                                        <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 w-64 p-2 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity z-10 pointer-events-none">
                                            <p className="mb-1 font-medium">Population & Income</p>
                                            <p className="text-gray-300">Key demographic indicators that help you understand the local market. Higher population density often means stronger rental demand, while higher median income suggests better tenant quality and potential for rent increases. These metrics are crucial for evaluating investment potential and market stability.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="bg-white/50 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 mb-1">Population</p>
                                    <p className="font-semibold text-gray-800">{demographicsData.demographics?.population_2020_count?.toLocaleString()}</p>
                                </div>
                                <div className="bg-white/50 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 mb-1">Median Age</p>
                                    <p className="font-semibold text-gray-800">{demographicsData.demographics?.population_median_age} years</p>
                                </div>
                                <div className="col-span-2 bg-white/50 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 mb-1">Median Household Income</p>
                                    <p className="font-semibold text-gray-800">${demographicsData.demographics?.median_household_income?.toLocaleString()}</p>
                                </div>
                            </div>
                        </div>
                        <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-5 rounded-lg shadow-sm h-[280px]">
                            <div className="flex items-center gap-x-2 mb-4">
                                <h4 className="text-base font-semibold text-gray-800 flex items-center gap-x-2">
                                    <FontAwesomeIcon icon={faShieldAlt} className="text-purple-500 h-4 w-4" />
                                    Safety & Risk
                                </h4>
                                <div className="relative">
                                    <div className="group">
                                        <FontAwesomeIcon icon={faCircleInfo} className="text-gray-400 h-4 w-4" />
                                        <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 w-64 p-2 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity z-10 pointer-events-none">
                                            <p className="mb-1 font-medium">Safety & Risk</p>
                                            <p className="text-gray-300">Important safety metrics that impact property value and tenant retention. Lower crime rates typically mean higher property values and better tenant quality. These indicators help you assess the neighborhood's stability and potential for long-term appreciation.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="bg-white/50 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 mb-1">Crime Risk Score</p>
                                    <div className="flex items-center gap-x-2">
                                        <p className={`font-semibold ${
                                            demographicsData.demographics?.crime_total_risk && demographicsData.demographics?.crime_total_risk > 110 ? 'text-red-500' : 
                                            demographicsData.demographics?.crime_total_risk && demographicsData.demographics?.crime_total_risk >= 80 && demographicsData.demographics?.crime_total_risk <= 110 ? 'text-yellow-500' : 
                                            'text-green-500'
                                        }`}>
                                            {demographicsData.demographics?.crime_total_risk}
                                        </p>
                                        <span className={`text-xs px-2 py-1 rounded-full ${
                                            demographicsData.demographics?.crime_total_risk && demographicsData.demographics?.crime_total_risk > 110 ? 'bg-red-50 text-red-700' : 
                                            demographicsData.demographics?.crime_total_risk && demographicsData.demographics?.crime_total_risk >= 80 && demographicsData.demographics?.crime_total_risk <= 110 ? 'bg-yellow-50 text-yellow-700' : 
                                            'bg-green-50 text-green-700'
                                        }`}>
                                            {demographicsData.demographics?.crime_total_risk && demographicsData.demographics?.crime_total_risk > 110 ? 'High Risk' : 
                                             demographicsData.demographics?.crime_total_risk && demographicsData.demographics?.crime_total_risk >= 80 && demographicsData.demographics?.crime_total_risk <= 110 ? 'Medium Risk' : 
                                             'Low Risk'}
                                        </span>
                                    </div>
                                </div>
                                <div className="bg-white/50 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 mb-1">Economic Stability</p>
                                    <div className="flex items-center gap-x-2">
                                        <p className={`font-semibold ${
                                            demographicsData.demographics?.average_household_income && demographicsData.demographics?.average_household_income > 75000 ? 'text-green-500' : 
                                            demographicsData.demographics?.average_household_income && demographicsData.demographics?.average_household_income > 50000 ? 'text-yellow-500' : 
                                            'text-red-500'
                                        }`}>
                                            ${demographicsData.demographics?.average_household_income?.toLocaleString()}
                                        </p>
                                        <span className={`text-xs px-2 py-1 rounded-full ${
                                            demographicsData.demographics?.average_household_income && demographicsData.demographics?.average_household_income > 75000 ? 'bg-green-50 text-green-700' : 
                                            demographicsData.demographics?.average_household_income && demographicsData.demographics?.average_household_income > 50000 ? 'bg-yellow-50 text-yellow-700' : 
                                            'bg-red-50 text-red-700'
                                        }`}>
                                            {demographicsData.demographics?.average_household_income && demographicsData.demographics?.average_household_income > 75000 ? 'High' : 
                                             demographicsData.demographics?.average_household_income && demographicsData.demographics?.average_household_income > 50000 ? 'Moderate' : 
                                             'Low'}
                                        </span>
                                    </div>
                                </div>
                                <div className="bg-white/50 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 mb-1">Income Variance</p>
                                    <p className="font-semibold text-gray-800">
                                        {demographicsData.demographics?.median_household_income && demographicsData.demographics?.average_household_income ? 
                                            `${(((demographicsData.demographics.average_household_income - demographicsData.demographics.median_household_income) / demographicsData.demographics.median_household_income) * 100).toFixed(1)}%` : 
                                            'N/A'
                                        }
                                    </p>
                                </div>
                                <div className="bg-white/50 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 mb-1">Data Source</p>
                                    <p className="font-semibold text-gray-800">{demographicsData.demographics?.vintage}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}