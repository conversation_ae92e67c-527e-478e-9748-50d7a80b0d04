'use client';

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPen, faCalculator, faWandMagicSparkles, faSpinner, faCheck, faPlus, faTimes, faExclamationTriangle, faHome, faShieldAlt, faWrench, faCog, faFileInvoiceDollar, faLightbulb, faTools, faPiggyBank } from "@fortawesome/free-solid-svg-icons";
import { useSearchParams, useRouter } from "next/navigation";
import { usePortfolio } from "@/context/PortfolioContext";
import { createClient } from '@/utils/supabase/client';

interface FinancialSectionItem {
    key: string;
    label: string;
    format: string;
    edit?: {isCalculated?: boolean, isAICalculation?: boolean};
    negative?: boolean;
    isSectionTotal?: boolean;
    isCalculated?: boolean;
    expandable?: boolean;
    subItems?: FinancialSectionItem[];
}

interface FinancialSection {
    title: string;
    items: FinancialSectionItem[];
}

interface Props {
    item: FinancialSectionItem;
    year: number;
    section: FinancialSection;
    getValue: (year: number, key: string) => string;
    handleInputChange: (year: number, key: string, value: string, source?: 'human' | 'ai' | 'calculator') => void;
    calculateSectionTotal: (year: number, section: FinancialSection) => number;
    units: { [key: string]: any }[];
    setUnits: React.Dispatch<React.SetStateAction<{ [key: string]: any }[]>>;
}

export default function WorkspaceDetailFinancialsItemEnhanced({ 
    item, 
    year, 
    section, 
    getValue, 
    handleInputChange, 
    calculateSectionTotal, 
    units, 
    setUnits 
}: Props) {
    console.log('✨ Enhanced component rendered for:', item.key, 'year:', year, 'label:', item.label);
    
    const [isEditing, setIsEditing] = useState(false);
    const [isCalculating, setIsCalculating] = useState(false);
    const [isAgenticCalculating, setIsAgenticCalculating] = useState(false);
    const [editValue, setEditValue] = useState('');
    const [showCalculator, setShowCalculator] = useState(false);
    const [showAgenticModal, setShowAgenticModal] = useState(false);
    const [showTaxCalculator, setShowTaxCalculator] = useState(false);
    const [taxRecords, setTaxRecords] = useState<any[]>([]);
    const [agenticProgress, setAgenticProgress] = useState<{
        step: string;
        completed: string[];
        current: string;
        error?: string;
        results?: any;
    }>({
        step: '',
        completed: [],
        current: '',
    });
    
    const searchParams = useSearchParams();
    const router = useRouter();
    const { selectedPortfolio } = usePortfolio();
    const addressId = searchParams.get('addressId');

    // Debug logging for showCalculator state changes
    useEffect(() => {
        console.log('🔍 showCalculator state changed to:', showCalculator, 'for item:', item.key, 'year:', year);
        if (showCalculator) {
            console.log('🖥️ Calculator modal is being rendered for:', item.key, 'year:', year);
            if (year === 1) {
                console.log('🔢 Apply for All Years button should be visible (year === 1)');
            } else {
                console.log('❌ Apply for All Years button will NOT be visible (year =', year, ')');
            }
        }
    }, [showCalculator, item.key, year]);

    // Format value for display - no cents, dollars only
    const formatValue = (value: string, format: string) => {
        const numValue = parseFloat(value || '0');

        // Return empty string if value is NaN, null, undefined, or absolute value is zero
        if (isNaN(numValue) || Math.abs(numValue) === 0) return '';

        switch (format) {
            case 'currency':
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                }).format(Math.abs(numValue));
            case 'percentage':
                return `${numValue.toFixed(1)}%`;
            case 'multiplier':
                return `${numValue.toFixed(2)}×`;
            default:
                return Math.round(numValue).toLocaleString();
        }
    };

    // Get current value or calculated value
    const getCurrentValue = () => {
        if (item.isCalculated && item.isSectionTotal) {
            return calculateSectionTotal(year, section).toString();
        }
        return getValue(year, item.key) || '0';
    };

    const displayValue = getCurrentValue();
    const isNegative = item.negative || parseFloat(displayValue) < 0;

    // Handle manual edit
    const handleStartEdit = () => {
        setIsEditing(true);
        setEditValue(displayValue);
    };

    const handleSaveEdit = () => {
        let value = editValue;
        if (item.negative && !value.startsWith('-') && parseFloat(value) > 0) {
            value = '-' + value;
        }
        handleInputChange(year, item.key, value, 'human');
        setIsEditing(false);
    };

    const handleCancelEdit = () => {
        setIsEditing(false);
        setEditValue('');
    };

    // Handle calculator
    const handleCalculatorClick = async () => {
        console.log('🔧 Calculator button clicked for item:', item.key, 'year:', year);
        
        if (item.key === 'property_taxes') {
            console.log('🏠 Opening property tax calculator');
            // Show tax calculator modal for property taxes
            setShowTaxCalculator(true);
            
            // Fetch real tax records from database
            try {
                const supabase = createClient();
                
                const { data: taxHistory, error } = await supabase
                    .from('tax_history')
                    .select('*')
                    .eq('address_id', addressId)
                    .order('year', { ascending: false })
                    .limit(5);

                if (taxHistory && taxHistory.length > 0) {
                    setTaxRecords(taxHistory);
                } else {
                    // No tax records found - show empty state
                    setTaxRecords([]);
                }
            } catch (error) {
                console.error('Error fetching tax records:', error);
                setTaxRecords([]);
            }
        } else {
            console.log('🧮 Opening enhanced calculator modal for:', item.key);
            setShowCalculator(true);
        }
    };

    // Handle agentic calculation
    const handleAgenticClick = () => {
        setShowAgenticModal(true);
    };

    const handleAgenticCalculation = async (calculateForAllYears = false) => {
        setIsAgenticCalculating(true);
        setShowAgenticModal(false);
        
        // Reset progress
        setAgenticProgress({
            step: 'initializing',
            completed: [],
            current: 'Starting calculation...',
        });

        try {
            // Define calculation steps based on item type
            const getCalculationSteps = (itemKey: string) => {
                switch (itemKey) {
                    case 'property_taxes':
                        return ['fetching_tax_data', 'searching_online', 'calculating_values'];
                    case 'insurance':
                        return ['analyzing_property', 'searching_insurance_rates', 'calculating_coverage'];
                    case 'repairs_maintenance':
                        return ['analyzing_images', 'condition_assessment', 'estimating_costs'];
                    case 'maintenance':
                        return ['property_analysis', 'unit_assessment', 'cost_estimation'];
                    case 'professional_fees':
                        return ['market_research', 'service_analysis', 'fee_calculation'];
                    case 'utilities':
                        return ['utility_research', 'usage_analysis', 'cost_calculation'];
                    case 'services':
                        return ['service_research', 'market_analysis', 'pricing_calculation'];
                    case 'reserve_replacement':
                        return ['asset_analysis', 'lifecycle_assessment', 'reserve_calculation'];
                    default:
                        return ['data_collection', 'analysis', 'calculation'];
                }
            };

            const steps = getCalculationSteps(item.key);
            
            const response = await fetch('/api/financial-agentic-calculation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    itemKey: item.key,
                    year: calculateForAllYears ? 'all' : year,
                    addressId: addressId,
                    units: units,
                    currentValue: displayValue,
                    format: item.format,
                    progressCallback: (progress: any) => {
                        setAgenticProgress(prev => ({
                            ...prev,
                            ...progress
                        }));
                    }
                })
            });

            const data = await response.json();
            
            if (data.success) {
                setAgenticProgress(prev => ({
                    ...prev,
                    step: 'completed',
                    current: 'Calculation completed successfully!',
                    results: data
                }));

                if (calculateForAllYears && data.values) {
                    // Update all years
                    Object.entries(data.values).forEach(([yearKey, value]) => {
                        let finalValue = value as string;
                        
                        // Ensure negative values for expenses
                        if (item.negative && parseFloat(finalValue) > 0) {
                            finalValue = '-' + finalValue;
                        }
                        
                        handleInputChange(parseInt(yearKey), item.key, finalValue, 'ai');
                    });
                } else if (data.value) {
                    // Update single year
                    let finalValue = data.value;
                    
                    // Ensure negative values for expenses
                    if (item.negative && parseFloat(finalValue) > 0) {
                        finalValue = '-' + finalValue;
                    }
                    
                    handleInputChange(year, item.key, finalValue, 'ai');
                }

                // Close progress modal after 2 seconds
                setTimeout(() => {
                    setAgenticProgress({
                        step: '',
                        completed: [],
                        current: '',
                    });
                }, 2000);
            } else {
                setAgenticProgress(prev => ({
                    ...prev,
                    step: 'error',
                    error: data.error || 'Calculation failed'
                }));
            }
        } catch (error) {
            console.error('Error calculating agentically:', error);
            setAgenticProgress(prev => ({
                ...prev,
                step: 'error',
                error: 'Network error occurred'
            }));
        } finally {
            setIsAgenticCalculating(false);
        }
    };

    // Navigate to property units
    const handleNavigateToUnits = () => {
        if (addressId) {
            // Navigate to property units section
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('section', 'units');
            router.push(currentUrl.toString());
        }
    };

    // Handle calculator for all years with CPI growth
    const handleCalculatorForAllYears = async () => {
        console.log('🚀 handleCalculatorForAllYears called for item:', item.key);
        console.log('📍 addressId:', addressId);
        console.log('📁 selectedPortfolio:', selectedPortfolio);
        console.log('🏠 units:', units);
        
        setIsCalculating(true);
        
        try {
            // Calculate base value for Year 1
            let baseValue: number;
            
            if (item.key === 'rental_income') {
                baseValue = units.reduce((sum, unit) => sum + (unit.rent * 12 || 0), 0);
                console.log('💰 Calculated rental income baseValue:', baseValue);
            } else {
                const grossIncome = parseFloat(getValue(1, 'rental_income') || '0');
                console.log('📊 Gross income for calculation:', grossIncome);
                const rate = item.key === 'other_income' ? 0.03 :
                           item.key === 'vacancy_loss' ? 0.05 :
                           item.key === 'credit_loss' ? 0.02 : 0;
                console.log('📈 Rate for', item.key, ':', rate);
                let amount = grossIncome * rate;
                if (item.negative) amount = -Math.abs(amount);
                baseValue = amount;
                console.log('💰 Calculated baseValue for', item.key, ':', baseValue);
            }

            // Get CPI data for multi-year calculations
            let cpiRates = [3.2, 2.8, 2.5, 2.3, 2.1]; // Default rates
            console.log('📈 Using default CPI rates:', cpiRates);
            
            try {
                console.log('🌐 Fetching CPI data from API...');
                const response = await fetch('/api/financial-agentic-calculation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        itemKey: 'get_cpi_data',
                        year: 'all',
                        addressId: addressId,
                        units: units,
                        currentValue: baseValue.toString(),
                        format: item.format
                    })
                });

                const data = await response.json();
                console.log('📊 CPI API response:', data);
                if (data.success && data.cpiRates) {
                    cpiRates = data.cpiRates;
                    console.log('✅ Updated CPI rates from API:', cpiRates);
                }
            } catch (error) {
                console.warn('⚠️ Failed to get CPI data, using defaults:', error);
            }
            
            // Calculate all values first, then apply them
            const calculatedValues: { [year: number]: number } = {};
            
            // Year 1 base value
            calculatedValues[1] = baseValue;
            
            // Calculate CPI-adjusted values for Years 2-10
            let currentValue = baseValue;
            for (let yearNum = 2; yearNum <= 10; yearNum++) {
                const cpiRate = cpiRates[yearNum - 2] || 2.5; // Default to 2.5% if not available
                currentValue = currentValue * (1 + cpiRate / 100);
                calculatedValues[yearNum] = Math.round(currentValue);
            }
            
            console.log('🧮 Calculated values for all years:', calculatedValues);
            
            // Validate that we have an addressId before proceeding
            if (!addressId) {
                console.error('❌ No addressId available - cannot save to database');
                throw new Error('No addressId available');
            }
            
            // First save all to database
            console.log('💾 Starting database saves...');
            const savePromises = Object.entries(calculatedValues).map(([yearNum, value]) => {
                const year = parseInt(yearNum);
                console.log(`💾 Saving ${item.key} for year ${year} with value ${value}`);
                
                return fetch('/api/financial-save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        addressId: addressId,
                        portfolioId: selectedPortfolio?.id,
                        year: year,
                        key: item.key,
                        value: value,
                        source: 'calculator'
                    })
                }).then(response => {
                    console.log(`✅ Save response for year ${year}:`, response.status);
                    return response.json();
                }).then(data => {
                    console.log(`📊 Save data for year ${year}:`, data);
                    return data;
                }).catch(error => {
                    console.error(`❌ Save error for year ${year}:`, error);
                    throw error;
                });
            });
            
            // Wait for all saves to complete
            const saveResults = await Promise.all(savePromises);
            console.log('✅ All values saved to database:', saveResults);
            
            // Now update UI state for each year with delays
            console.log('🎨 Starting UI updates...');
            for (let yearNum = 1; yearNum <= 10; yearNum++) {
                const value = calculatedValues[yearNum];
                if (value !== undefined) {
                    setTimeout(() => {
                        console.log(`🎨 Updating UI for year ${yearNum} with value ${value}`);
                        handleInputChange(yearNum, item.key, value.toString(), 'calculator');
                    }, yearNum * 100); // 100ms delay between each year
                }
            }
            
            setShowCalculator(false);
            console.log('🎉 handleCalculatorForAllYears completed successfully');
            
        } catch (error) {
            console.error('❌ Error in handleCalculatorForAllYears:', error);
            alert(`Error calculating for all years: ${error instanceof Error ? error.message : 'Unknown error'}`);
        } finally {
            setIsCalculating(false);
        }
    };

    // Determine which buttons to show
    const showPenButton = !item.isCalculated;
    const showCalculatorButton = ['rental_income', 'other_income', 'vacancy_loss', 'credit_loss'].includes(item.key);
    const showAgenticButton = ['rental_income', 'other_income', 'vacancy_loss', 'credit_loss'].includes(item.key);

    return (
        <div className="group relative">
            <div className="flex items-center justify-between min-h-[32px]">
                {/* Value Display/Edit */}
                <div className="flex-1 pr-2">
                    {isEditing ? (
                        <div className="flex items-center gap-2">
                            <input
                                type="number"
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                className="w-24 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                                autoFocus
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') handleSaveEdit();
                                    if (e.key === 'Escape') handleCancelEdit();
                                }}
                            />
                            <button
                                onClick={handleSaveEdit}
                                className="p-1 text-green-600 hover:text-green-800"
                            >
                                <FontAwesomeIcon icon={faCheck} className="h-3 w-3" />
                            </button>
                        </div>
                    ) : (
                        <div className={`text-sm font-medium ${isNegative && item.format === 'currency' ? 'text-red-600' : 'text-gray-900'}`}>
                            {isAgenticCalculating ? (
                                <div className="flex items-center gap-2">
                                    <FontAwesomeIcon icon={faSpinner} className="h-3 w-3 animate-spin text-indigo-600" />
                                    <span className="text-indigo-600">Calculating...</span>
                                </div>
                            ) : (() => {
                                const formattedValue = formatValue(displayValue, item.format);
                                return formattedValue || "";
                            })()}
                        </div>
                    )}
                </div>

                {/* Action Buttons */}
                <div className="absolute right-0 top-1/2 transform -translate-y-1/2 flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/90 backdrop-blur-sm rounded px-1 shadow-sm border border-gray-200 z-10">
                    {/* Pen Button - Manual Edit */}
                    {showPenButton && (
                        <button
                            onClick={handleStartEdit}
                            className="p-1.5 text-gray-400 hover:text-indigo-600 hover:bg-indigo-50 rounded transition-colors"
                            title="Edit manually"
                        >
                            <FontAwesomeIcon icon={faPen} className="h-3 w-3" />
                        </button>
                    )}

                    {/* Calculator Button */}
                    {showCalculatorButton && (
                        <button
                            onClick={handleCalculatorClick}
                            className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                            title="Use calculator"
                        >
                            <FontAwesomeIcon icon={faCalculator} className="h-3 w-3" />
                        </button>
                    )}

                    {/* Agentic Button */}
                    {showAgenticButton && (
                        <button
                            onClick={handleAgenticClick}
                            disabled={isAgenticCalculating}
                            className="group/wand p-1.5 text-gray-400 hover:text-purple-600 hover:bg-purple-50 rounded transition-all duration-200 disabled:opacity-50 relative"
                            title="Calculate with AI"
                        >
                            <FontAwesomeIcon 
                                icon={faWandMagicSparkles} 
                                className="h-3 w-3 group-hover/wand:drop-shadow-[0_0_4px_rgba(147,51,234,0.6)] group-hover/wand:scale-110 transition-all duration-200" 
                            />
                        </button>
                    )}
                </div>
            </div>

            {/* Calculator Modal */}
            {showCalculator && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                    <div className="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] flex flex-col">
                        {/* Header */}
                        <div className="p-6 border-b border-gray-200">
                            <h3 className="text-lg font-semibold">Calculator - {item.label}</h3>
                        </div>
                        
                        {/* Scrollable Content */}
                        <div className="flex-1 overflow-y-auto p-6">
                            {item.key === 'rental_income' && (
                                <div className="space-y-4">
                                    <div className="text-sm text-gray-600">
                                        Calculate from units or enter manually
                                    </div>
                                    
                                    {year === 1 && (
                                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                            <div className="text-sm text-blue-800 font-medium mb-1">Multi-Year Calculator</div>
                                            <div className="text-xs text-blue-700">
                                                Use "Apply for All Years" to calculate Year 1 and automatically apply CPI growth for Years 2-5
                                            </div>
                                        </div>
                                    )}
                                    {units.length > 0 ? (
                                        <div className="space-y-2">
                                            <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-lg">
                                                {units.map((unit, index) => (
                                                    <div key={index} className="flex items-center justify-between px-3 py-2 border-b border-gray-100 last:border-b-0">
                                                        <span className="text-sm font-medium">Unit {unit.unit}</span>
                                                        <span className="text-sm text-gray-600">${unit.rent ? (unit.rent * 12).toLocaleString() : '0'}/year</span>
                                                    </div>
                                                ))}
                                            </div>
                                            <div className="pt-2 border-t border-gray-300 bg-blue-50 p-3 rounded-lg">
                                                <div className="text-sm font-semibold text-blue-900">
                                                    Total Annual: ${units.reduce((sum, unit) => sum + (unit.rent * 12 || 0), 0).toLocaleString()}
                                                </div>
                                                <div className="text-xs text-blue-700 mt-1">
                                                    {units.length} unit{units.length !== 1 ? 's' : ''}
                                                </div>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="text-center py-4">
                                            <p className="text-sm text-gray-500 mb-3">No units found</p>
                                            <button
                                                onClick={handleNavigateToUnits}
                                                className="inline-flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                                            >
                                                <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
                                                Add Units
                                            </button>
                                        </div>
                                    )}
                                </div>
                            )}

                            {(item.key === 'other_income' || item.key === 'vacancy_loss' || item.key === 'credit_loss') && (
                                <div className="space-y-4">
                                    <div className="text-sm text-gray-600">
                                        Default percentage calculation
                                    </div>
                                    
                                    {year === 1 && (
                                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                            <div className="text-sm text-blue-800 font-medium mb-1">Multi-Year Calculator</div>
                                            <div className="text-xs text-blue-700">
                                                Use "Apply for All Years" to calculate Year 1 and automatically apply CPI growth for Years 2-5
                                            </div>
                                        </div>
                                    )}
                                    <div className="space-y-2">
                                        <div className="text-sm">
                                            <span className="font-medium">Gross Scheduled Income:</span> {formatValue(getValue(year, 'rental_income') || '0', 'currency') || ''}
                                        </div>
                                        <div className="text-sm">
                                            <span className="font-medium">Default Rate:</span> {
                                                item.key === 'other_income' ? '3%' :
                                                item.key === 'vacancy_loss' ? '5%' :
                                                item.key === 'credit_loss' ? '2%' : ''
                                            }
                                        </div>
                                        <div className="text-sm font-medium">
                                            <span>Calculated Amount:</span> {
                                                (() => {
                                                    const grossIncome = parseFloat(getValue(year, 'rental_income') || '0');
                                                    const rate = item.key === 'other_income' ? 0.03 :
                                                               item.key === 'vacancy_loss' ? 0.05 :
                                                               item.key === 'credit_loss' ? 0.02 : 0;
                                                    const amount = grossIncome * rate;
                                                    return formatValue(Math.abs(amount).toString(), 'currency') || '';
                                                })()
                                            }
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Footer with Buttons */}
                        <div className="p-6 border-t border-gray-200">
                            <div className="flex justify-end gap-3">
                            <button
                                onClick={() => setShowCalculator(false)}
                                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={async () => {
                                    let calculatedValue: number;
                                    
                                    if (item.key === 'rental_income') {
                                        calculatedValue = units.reduce((sum, unit) => sum + (unit.rent * 12 || 0), 0);
                                    } else {
                                        const grossIncome = parseFloat(getValue(year, 'rental_income') || '0');
                                        const rate = item.key === 'other_income' ? 0.03 :
                                                   item.key === 'vacancy_loss' ? 0.05 :
                                                   item.key === 'credit_loss' ? 0.02 : 0;
                                        let amount = grossIncome * rate;
                                        if (item.negative) amount = -Math.abs(amount);
                                        calculatedValue = amount;
                                    }
                                    
                                    // Update UI
                                    handleInputChange(year, item.key, calculatedValue.toString(), 'calculator');
                                    
                                    // Save to database
                                    try {
                                        const saveResponse = await fetch('/api/financial-save', {
                                            method: 'POST',
                                            headers: {
                                                'Content-Type': 'application/json',
                                            },
                                                                                    body: JSON.stringify({
                                            addressId: addressId,
                                            portfolioId: selectedPortfolio?.id,
                                            year: year,
                                            key: item.key,
                                            value: calculatedValue,
                                            source: 'calculator'
                                        })
                                        });
                                        
                                        if (!saveResponse.ok) {
                                            console.warn(`Failed to save ${item.key} for year ${year}`);
                                        }
                                    } catch (saveError) {
                                        console.error(`Error saving ${item.key} for year ${year}:`, saveError);
                                    }
                                    
                                    setShowCalculator(false);
                                }}
                                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                Apply Year {year}
                            </button>
                            {year === 1 && (
                                <button
                                    onClick={() => {
                                        console.log('🔥 Apply for All Years button clicked!');
                                        handleCalculatorForAllYears();
                                    }}
                                    disabled={isCalculating}
                                    className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {isCalculating ? (
                                        <div className="flex items-center gap-2">
                                            <FontAwesomeIcon icon={faSpinner} className="h-3 w-3 animate-spin" />
                                            <span>Calculating...</span>
                                        </div>
                                    ) : (
                                        'Apply for All Years'
                                    )}
                                </button>
                            )}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Enhanced Agentic Modal */}
            {showAgenticModal && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                    <div className="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] flex flex-col">
                        {/* Header */}
                        <div className="p-6 border-b border-gray-200">
                            <div className="flex items-center gap-3">
                                <FontAwesomeIcon 
                                    icon={
                                        item.key === 'property_taxes' ? faHome :
                                        item.key === 'insurance' ? faShieldAlt :
                                        item.key === 'repairs_maintenance' ? faWrench :
                                        item.key === 'maintenance' ? faCog :
                                        item.key === 'professional_fees' ? faFileInvoiceDollar :
                                        item.key === 'utilities' ? faLightbulb :
                                        item.key === 'services' ? faTools :
                                        item.key === 'reserve_replacement' ? faPiggyBank :
                                        faWandMagicSparkles
                                    } 
                                    className="h-5 w-5 text-purple-600" 
                                />
                                <h3 className="text-lg font-semibold">AI Calculation - {item.label}</h3>
                            </div>
                        </div>
                        
                        {/* Scrollable Content */}
                        <div className="flex-1 overflow-y-auto p-6">
                            <div className="space-y-4">
                                <div className="text-sm text-gray-600">
                                    {item.key === 'property_taxes' && "AI will check tax records and search online for current property tax rates in your area"}
                                    {item.key === 'insurance' && "AI will research insurance rates based on your property address and square footage"}
                                    {item.key === 'repairs_maintenance' && "AI will analyze property images and estimate yearly repair costs based on condition"}
                                    {item.key === 'maintenance' && "AI will estimate maintenance costs based on units, square footage, and building info"}
                                    {item.key === 'professional_fees' && "AI will research professional service fees (property management, legal, accounting) in your area"}
                                    {item.key === 'utilities' && "AI will research utility costs for your property type and location"}
                                    {item.key === 'services' && "AI will research service costs (landscaping, cleaning, security) for your area"}
                                    {item.key === 'reserve_replacement' && "AI will analyze building assets and calculate optimal replacement reserves"}
                                    {!['property_taxes', 'insurance', 'repairs_maintenance', 'maintenance', 'professional_fees', 'utilities', 'services', 'reserve_replacement'].includes(item.key) && "AI will search for market data and calculate optimized values"}
                                </div>
                                
                                {year === 1 && (
                                    <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-4">
                                        <div className="flex items-center gap-2 mb-2">
                                            <FontAwesomeIcon icon={faWandMagicSparkles} className="h-4 w-4 text-purple-600" />
                                            <span className="text-sm text-purple-800 font-medium">Multi-Year AI Calculation</span>
                                        </div>
                                        <div className="text-xs text-purple-700">
                                            Calculate Year 1 using AI research and automatically apply CPI growth for Years 2-5
                                        </div>
                                    </div>
                                )}

                                {item.key === 'property_taxes' && (
                                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                        <div className="text-sm text-blue-800 font-medium mb-2">Tax Calculation Options</div>
                                        <div className="space-y-2 text-xs text-blue-700">
                                            <div>• Check existing tax records in database</div>
                                            <div>• Search online for current tax rates</div>
                                            <div>• Apply CPI adjustments for future years</div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Footer with Buttons */}
                        <div className="p-6 border-t border-gray-200">
                            <div className="flex justify-end gap-3">
                                <button
                                    onClick={() => setShowAgenticModal(false)}
                                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={() => handleAgenticCalculation(false)}
                                    className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors shadow-lg hover:shadow-xl transform hover:scale-105 transition-all"
                                >
                                    <div className="flex items-center gap-2">
                                        <FontAwesomeIcon icon={faWandMagicSparkles} className="h-4 w-4" />
                                        Calculate Year {year}
                                    </div>
                                </button>
                                {year === 1 && (
                                    <button
                                        onClick={() => handleAgenticCalculation(true)}
                                        className="px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all shadow-lg hover:shadow-xl transform hover:scale-105"
                                    >
                                        <div className="flex items-center gap-2">
                                            <FontAwesomeIcon icon={faWandMagicSparkles} className="h-4 w-4 animate-pulse" />
                                            Calculate All Years
                                        </div>
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Progress Modal */}
            {agenticProgress.step && (
                <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[60] flex items-center justify-center p-4">
                    <div className="bg-white rounded-xl shadow-2xl max-w-lg w-full">
                        {/* Header */}
                        <div className="p-6 border-b border-gray-200">
                            <div className="flex items-center gap-3">
                                <div className="relative">
                                    <FontAwesomeIcon 
                                        icon={faWandMagicSparkles} 
                                        className="h-6 w-6 text-purple-600 animate-pulse drop-shadow-[0_0_8px_rgba(147,51,234,0.6)]" 
                                    />
                                    {agenticProgress.step === 'completed' && (
                                        <FontAwesomeIcon 
                                            icon={faCheck} 
                                            className="absolute -top-1 -right-1 h-3 w-3 text-green-600 bg-white rounded-full" 
                                        />
                                    )}
                                    {agenticProgress.step === 'error' && (
                                        <FontAwesomeIcon 
                                            icon={faExclamationTriangle} 
                                            className="absolute -top-1 -right-1 h-3 w-3 text-red-600 bg-white rounded-full" 
                                        />
                                    )}
                                </div>
                                <h3 className="text-lg font-semibold">AI Calculating {item.label}</h3>
                            </div>
                        </div>
                        
                        {/* Progress Content */}
                        <div className="p-6">
                            <div className="space-y-4">
                                {/* Current Step */}
                                <div className="text-center">
                                    <div className="text-sm font-medium text-gray-900 mb-2">
                                        {agenticProgress.current}
                                    </div>
                                    
                                    {agenticProgress.step === 'initializing' && (
                                        <div className="flex justify-center">
                                            <FontAwesomeIcon icon={faSpinner} className="h-6 w-6 text-purple-600 animate-spin" />
                                        </div>
                                    )}
                                    
                                    {agenticProgress.step === 'completed' && (
                                        <div className="flex justify-center">
                                            <div className="bg-green-100 rounded-full p-3">
                                                <FontAwesomeIcon icon={faCheck} className="h-6 w-6 text-green-600" />
                                            </div>
                                        </div>
                                    )}
                                    
                                    {agenticProgress.step === 'error' && (
                                        <div className="space-y-3">
                                            <div className="flex justify-center">
                                                <div className="bg-red-100 rounded-full p-3">
                                                    <FontAwesomeIcon icon={faExclamationTriangle} className="h-6 w-6 text-red-600" />
                                                </div>
                                            </div>
                                            <div className="text-sm text-red-600">
                                                {agenticProgress.error}
                                            </div>
                                            <button
                                                onClick={() => setAgenticProgress({ step: '', completed: [], current: '' })}
                                                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                                            >
                                                Close
                                            </button>
                                        </div>
                                    )}
                                </div>

                                {/* Progress Steps */}
                                {agenticProgress.step !== 'error' && (
                                    <div className="space-y-2">
                                        {item.key === 'property_taxes' && (
                                            <>
                                                <div className={`flex items-center gap-3 p-2 rounded ${agenticProgress.completed.includes('fetching_tax_data') ? 'bg-green-50' : agenticProgress.current.includes('tax data') ? 'bg-blue-50' : 'bg-gray-50'}`}>
                                                    <FontAwesomeIcon 
                                                        icon={agenticProgress.completed.includes('fetching_tax_data') ? faCheck : faSpinner} 
                                                        className={`h-4 w-4 ${agenticProgress.completed.includes('fetching_tax_data') ? 'text-green-600' : agenticProgress.current.includes('tax data') ? 'text-blue-600 animate-spin' : 'text-gray-400'}`} 
                                                    />
                                                    <span className="text-sm">Checking tax records database</span>
                                                </div>
                                                <div className={`flex items-center gap-3 p-2 rounded ${agenticProgress.completed.includes('searching_online') ? 'bg-green-50' : agenticProgress.current.includes('online') ? 'bg-blue-50' : 'bg-gray-50'}`}>
                                                    <FontAwesomeIcon 
                                                        icon={agenticProgress.completed.includes('searching_online') ? faCheck : faSpinner} 
                                                        className={`h-4 w-4 ${agenticProgress.completed.includes('searching_online') ? 'text-green-600' : agenticProgress.current.includes('online') ? 'text-blue-600 animate-spin' : 'text-gray-400'}`} 
                                                    />
                                                    <span className="text-sm">Searching online tax rates</span>
                                                </div>
                                                <div className={`flex items-center gap-3 p-2 rounded ${agenticProgress.completed.includes('calculating_values') ? 'bg-green-50' : agenticProgress.current.includes('calculating') ? 'bg-blue-50' : 'bg-gray-50'}`}>
                                                    <FontAwesomeIcon 
                                                        icon={agenticProgress.completed.includes('calculating_values') ? faCheck : faSpinner} 
                                                        className={`h-4 w-4 ${agenticProgress.completed.includes('calculating_values') ? 'text-green-600' : agenticProgress.current.includes('calculating') ? 'text-blue-600 animate-spin' : 'text-gray-400'}`} 
                                                    />
                                                    <span className="text-sm">Calculating final values</span>
                                                </div>
                                            </>
                                        )}

                                        {item.key === 'insurance' && (
                                            <>
                                                <div className={`flex items-center gap-3 p-2 rounded ${agenticProgress.completed.includes('analyzing_property') ? 'bg-green-50' : agenticProgress.current.includes('property') ? 'bg-blue-50' : 'bg-gray-50'}`}>
                                                    <FontAwesomeIcon 
                                                        icon={agenticProgress.completed.includes('analyzing_property') ? faCheck : faSpinner} 
                                                        className={`h-4 w-4 ${agenticProgress.completed.includes('analyzing_property') ? 'text-green-600' : agenticProgress.current.includes('property') ? 'text-blue-600 animate-spin' : 'text-gray-400'}`} 
                                                    />
                                                    <span className="text-sm">Analyzing property details</span>
                                                </div>
                                                <div className={`flex items-center gap-3 p-2 rounded ${agenticProgress.completed.includes('searching_insurance_rates') ? 'bg-green-50' : agenticProgress.current.includes('insurance') ? 'bg-blue-50' : 'bg-gray-50'}`}>
                                                    <FontAwesomeIcon 
                                                        icon={agenticProgress.completed.includes('searching_insurance_rates') ? faCheck : faSpinner} 
                                                        className={`h-4 w-4 ${agenticProgress.completed.includes('searching_insurance_rates') ? 'text-green-600' : agenticProgress.current.includes('insurance') ? 'text-blue-600 animate-spin' : 'text-gray-400'}`} 
                                                    />
                                                    <span className="text-sm">Researching insurance rates</span>
                                                </div>
                                                <div className={`flex items-center gap-3 p-2 rounded ${agenticProgress.completed.includes('calculating_coverage') ? 'bg-green-50' : agenticProgress.current.includes('coverage') ? 'bg-blue-50' : 'bg-gray-50'}`}>
                                                    <FontAwesomeIcon 
                                                        icon={agenticProgress.completed.includes('calculating_coverage') ? faCheck : faSpinner} 
                                                        className={`h-4 w-4 ${agenticProgress.completed.includes('calculating_coverage') ? 'text-green-600' : agenticProgress.current.includes('coverage') ? 'text-blue-600 animate-spin' : 'text-gray-400'}`} 
                                                    />
                                                    <span className="text-sm">Calculating coverage costs</span>
                                                </div>
                                            </>
                                        )}

                                        {item.key === 'repairs_maintenance' && (
                                            <>
                                                <div className={`flex items-center gap-3 p-2 rounded ${agenticProgress.completed.includes('analyzing_images') ? 'bg-green-50' : agenticProgress.current.includes('images') ? 'bg-blue-50' : 'bg-gray-50'}`}>
                                                    <FontAwesomeIcon 
                                                        icon={agenticProgress.completed.includes('analyzing_images') ? faCheck : faSpinner} 
                                                        className={`h-4 w-4 ${agenticProgress.completed.includes('analyzing_images') ? 'text-green-600' : agenticProgress.current.includes('images') ? 'text-blue-600 animate-spin' : 'text-gray-400'}`} 
                                                    />
                                                    <span className="text-sm">Analyzing property images</span>
                                                </div>
                                                <div className={`flex items-center gap-3 p-2 rounded ${agenticProgress.completed.includes('condition_assessment') ? 'bg-green-50' : agenticProgress.current.includes('condition') ? 'bg-blue-50' : 'bg-gray-50'}`}>
                                                    <FontAwesomeIcon 
                                                        icon={agenticProgress.completed.includes('condition_assessment') ? faCheck : faSpinner} 
                                                        className={`h-4 w-4 ${agenticProgress.completed.includes('condition_assessment') ? 'text-green-600' : agenticProgress.current.includes('condition') ? 'text-blue-600 animate-spin' : 'text-gray-400'}`} 
                                                    />
                                                    <span className="text-sm">Assessing property condition</span>
                                                </div>
                                                <div className={`flex items-center gap-3 p-2 rounded ${agenticProgress.completed.includes('estimating_costs') ? 'bg-green-50' : agenticProgress.current.includes('costs') ? 'bg-blue-50' : 'bg-gray-50'}`}>
                                                    <FontAwesomeIcon 
                                                        icon={agenticProgress.completed.includes('estimating_costs') ? faCheck : faSpinner} 
                                                        className={`h-4 w-4 ${agenticProgress.completed.includes('estimating_costs') ? 'text-green-600' : agenticProgress.current.includes('costs') ? 'text-blue-600 animate-spin' : 'text-gray-400'}`} 
                                                    />
                                                    <span className="text-sm">Estimating repair costs</span>
                                                </div>
                                            </>
                                        )}

                                        {/* Generic progress for other expense types */}
                                        {!['property_taxes', 'insurance', 'repairs_maintenance'].includes(item.key) && (
                                            <>
                                                <div className={`flex items-center gap-3 p-2 rounded ${agenticProgress.completed.includes('data_collection') ? 'bg-green-50' : agenticProgress.current.includes('data') ? 'bg-blue-50' : 'bg-gray-50'}`}>
                                                    <FontAwesomeIcon 
                                                        icon={agenticProgress.completed.includes('data_collection') ? faCheck : faSpinner} 
                                                        className={`h-4 w-4 ${agenticProgress.completed.includes('data_collection') ? 'text-green-600' : agenticProgress.current.includes('data') ? 'text-blue-600 animate-spin' : 'text-gray-400'}`} 
                                                    />
                                                    <span className="text-sm">Collecting market data</span>
                                                </div>
                                                <div className={`flex items-center gap-3 p-2 rounded ${agenticProgress.completed.includes('analysis') ? 'bg-green-50' : agenticProgress.current.includes('analysis') ? 'bg-blue-50' : 'bg-gray-50'}`}>
                                                    <FontAwesomeIcon 
                                                        icon={agenticProgress.completed.includes('analysis') ? faCheck : faSpinner} 
                                                        className={`h-4 w-4 ${agenticProgress.completed.includes('analysis') ? 'text-green-600' : agenticProgress.current.includes('analysis') ? 'text-blue-600 animate-spin' : 'text-gray-400'}`} 
                                                    />
                                                    <span className="text-sm">Analyzing information</span>
                                                </div>
                                                <div className={`flex items-center gap-3 p-2 rounded ${agenticProgress.completed.includes('calculation') ? 'bg-green-50' : agenticProgress.current.includes('calculation') ? 'bg-blue-50' : 'bg-gray-50'}`}>
                                                    <FontAwesomeIcon 
                                                        icon={agenticProgress.completed.includes('calculation') ? faCheck : faSpinner} 
                                                        className={`h-4 w-4 ${agenticProgress.completed.includes('calculation') ? 'text-green-600' : agenticProgress.current.includes('calculation') ? 'text-blue-600 animate-spin' : 'text-gray-400'}`} 
                                                    />
                                                    <span className="text-sm">Calculating values</span>
                                                </div>
                                            </>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Tax Calculator Modal */}
            {showTaxCalculator && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                    <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] flex flex-col">
                        {/* Header */}
                        <div className="p-6 border-b border-gray-200">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                    <FontAwesomeIcon icon={faHome} className="h-5 w-5 text-blue-600" />
                                    <h3 className="text-lg font-semibold">Property Tax Calculator</h3>
                                </div>
                                <button
                                    onClick={() => setShowTaxCalculator(false)}
                                    className="text-gray-400 hover:text-gray-600 transition-colors"
                                >
                                    <FontAwesomeIcon icon={faTimes} className="h-5 w-5" />
                                </button>
                            </div>
                        </div>
                        
                        {/* Content */}
                        <div className="flex-1 overflow-y-auto p-6">
                            {taxRecords.length > 0 ? (
                                <div className="space-y-4">
                                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                        <h4 className="text-sm font-medium text-blue-800 mb-2">Tax History Found</h4>
                                        <p className="text-xs text-blue-700">
                                            Select a tax record to apply to your financial projections. Values will be adjusted with CPI for future years.
                                        </p>
                                    </div>
                                    
                                    <div className="space-y-3">
                                        {taxRecords.map((record, index) => (
                                            <div key={index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                                <div className="flex items-center justify-between">
                                                    <div>
                                                        <div className="font-medium text-gray-900">
                                                            {record.year} Tax Year
                                                        </div>
                                                        <div className="text-sm text-gray-600">
                                                            Source: {record.source || 'Tax Assessment'}
                                                        </div>
                                                        {record.notes && (
                                                            <div className="text-xs text-gray-500 mt-1">
                                                                {record.notes}
                                                            </div>
                                                        )}
                                                    </div>
                                                    <div className="text-right">
                                                        <div className="text-lg font-semibold text-gray-900">
                                                            ${Math.round(record.amount).toLocaleString()}
                                                        </div>
                                                        <button
                                                            onClick={async () => {
                                                                // Apply this tax amount for all years with CPI adjustments
                                                                const baseAmount = record.amount;
                                                                
                                                                // Save to database for all years
                                                                for (let yearNum = 1; yearNum <= 5; yearNum++) {
                                                                    // Apply CPI growth for years 2-5
                                                                    let adjustedAmount = baseAmount;
                                                                    if (yearNum > 1) {
                                                                        const cpiGrowth = 0.025; // 2.5% average CPI
                                                                        adjustedAmount = baseAmount * Math.pow(1 + cpiGrowth, yearNum - 1);
                                                                    }
                                                                    
                                                                    // Make negative for expense
                                                                    const finalAmount = -Math.abs(adjustedAmount);
                                                                    
                                                                    try {
                                                                        const saveResponse = await fetch('/api/financial-save', {
                                                                            method: 'POST',
                                                                            headers: {
                                                                                'Content-Type': 'application/json',
                                                                            },
                                                                            body: JSON.stringify({
                                                                                addressId: addressId,
                                                                                portfolioId: selectedPortfolio?.id,
                                                                                year: yearNum,
                                                                                key: 'property_taxes',
                                                                                value: finalAmount,
                                                                                source: 'tax_records'
                                                                            })
                                                                        });
                                                                        
                                                                        if (saveResponse.ok) {
                                                                            // Update UI
                                                                            handleInputChange(yearNum, 'property_taxes', finalAmount.toString(), 'calculator');
                                                                        }
                                                                    } catch (error) {
                                                                        console.error(`Error saving tax for year ${yearNum}:`, error);
                                                                    }
                                                                }
                                                                
                                                                setShowTaxCalculator(false);
                                                            }}
                                                            className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                                                        >
                                                            Apply All Years
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                                        <FontAwesomeIcon icon={faExclamationTriangle} className="h-8 w-8 text-yellow-600 mb-4" />
                                        <h4 className="text-lg font-medium text-yellow-800 mb-2">No Tax Records Found</h4>
                                        <p className="text-sm text-yellow-700 mb-4">
                                            No property tax history found in the database for this address. Use the AI calculation to research current tax rates online.
                                        </p>
                                        <button
                                            onClick={() => {
                                                setShowTaxCalculator(false);
                                                handleAgenticClick();
                                            }}
                                            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                                        >
                                            <div className="flex items-center gap-2">
                                                <FontAwesomeIcon icon={faWandMagicSparkles} className="h-4 w-4" />
                                                Search Online
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                        
                        {/* Footer */}
                        <div className="p-6 border-t border-gray-200">
                            <div className="flex justify-end">
                                <button
                                    onClick={() => setShowTaxCalculator(false)}
                                    className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                                >
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
} 