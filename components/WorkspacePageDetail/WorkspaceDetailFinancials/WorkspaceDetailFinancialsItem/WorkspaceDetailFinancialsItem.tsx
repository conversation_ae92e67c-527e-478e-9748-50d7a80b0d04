'use client';

import React from 'react';
import { createPortal } from 'react-dom';
import { faCalculator, faPen, faHexagonNodes } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState, useEffect, useRef } from "react";
import WorkspaceDetailFinancialsItemCalculator from "./WorkspaceDetailFinancialsItemCalculator";
import WorkspaceDetailFinancialsItemDebtCalculator from "./WorkspaceDetailFinancialsItemDebtCalculator";
import WorkspaceDetailFinancialsItemAI from "./WorkspaceDetailFinancialsItemAI";
import { useSearchParams } from "next/navigation";
import Tooltip from "@/components/UI/Tooltip";
import { financialTooltips } from "@/constants/financialTooltips";

interface FinancialSectionItem {
    key: string;
    label: string;
    format: string;
    edit?: {isCalculated?: boolean, isAICalculation?: boolean};
    negative?: boolean;
    isSectionTotal?: boolean;
    isCalculated?: boolean;
    expandable?: boolean;
    subItems?: FinancialSectionItem[];
}

interface FinancialSection {
    title: string;
    items: FinancialSectionItem[];
}

interface YearValue {
    year: number;
    key: string;
    value: string;
    source: 'human' | 'ai' | 'calculator';
}

interface Props {
    item: FinancialSectionItem;
    year: number;
    section: FinancialSection;
    getValue: (year: number, key: string) => string;
    handleInputChange: (year: number, key: string, value: string, source?: 'human' | 'ai' | 'calculator') => void;
    handleBatchInputChange?: (values: YearValue[]) => void;
    calculateSectionTotal: (year: number, section: FinancialSection) => number;
    calculateNOI: (year: number) => number;
    units: { [key: string]: any }[];
    setUnits: React.Dispatch<React.SetStateAction<{ [key: string]: any }[]>>;
    metadata?: any; // Metadata from prop_financials
    disabled?: boolean; // Disable input during recalculation
}

const formatValue = (value: string | number, formatType: string): string => {
    if (!value && value !== 0) return '';
    // Convert to number
    const numberValue = typeof value === 'string' ? parseFloat(value) : value;
    // Check if it's a valid number
    if (isNaN(numberValue)) return '';

    // Return empty string if absolute value is zero
    if (Math.abs(numberValue) === 0) return '';

    // Round to nearest dollar for currency values
    const roundedValue = formatType === 'currency' ? Math.round(numberValue) : numberValue;
    
    // Format based on type
    if (formatType === 'percentage') {
        return `${Math.round(roundedValue * 100) / 100}%`;
    } else if (formatType === 'multiplier') {
        return `${Math.round(roundedValue * 100) / 100}×`;
    } else {
        // Currency format with proper negative formatting and rounding
        if (roundedValue < 0) {
            return `($${Math.abs(roundedValue).toLocaleString('en-US')})`;
        } else {
            return `$${roundedValue.toLocaleString('en-US')}`;
        }
    }
};

// Helper function to check if a value is negative
const isNegativeValue = (value: string | number): boolean => {
    if (!value && value !== 0) return false;
    const numberValue = typeof value === 'string' ? parseFloat(value) : value;
    return !isNaN(numberValue) && numberValue < 0;
};

// Helper function to get the current source from metadata
const getCurrentSource = (metadata: any, fieldKey: string): 'human' | 'ai' | 'calculator' | 'default' => {
    if (!metadata?.attribution?.[fieldKey]?.history) return 'default';

    const history = metadata.attribution[fieldKey].history;
    if (history.length === 0) return 'default';

    // Get the most recent entry
    const latestEntry = history[history.length - 1];
    return latestEntry.source || 'default';
};

// Helper function to get highlighting CSS classes based on source
const getCellHighlighting = (source: string): string => {
    switch(source) {
        case 'human': return 'border-l-4 border-yellow-400 bg-yellow-50';
        case 'ai':
        case 'calculator': return 'border-l-4 border-gray-200 bg-gray-50';
        default: return '';
    }
};

// Calculate value for items with sub-items
const calculateParentValue = (item: FinancialSectionItem, year: number, getValue: (year: number, key: string) => string): number => {
    if (!item.subItems || item.subItems.length === 0) {
        return 0;
    }
    
    let total = 0;
    item.subItems.forEach(subItem => {
        const subValue = parseFloat(getValue(year, subItem.key));
        if (!isNaN(subValue)) {
            if (subItem.negative) {
                total -= subValue;
            } else {
                total += subValue;
            }
        }
    });
    
    return total;
};

export default function WorkspaceDetailFinancialsItem({ item, year, section, getValue, handleInputChange, handleBatchInputChange, calculateSectionTotal, calculateNOI, units, setUnits, metadata, disabled = false }: Props) {
    const [isEditing, setIsEditing] = useState(false);
    const [isCalculatorVisible, setIsCalculatorVisible] = useState(false);
    const [isAICalculating, setIsAICalculating] = useState(false);
    const [isMounted, setIsMounted] = useState(false);
    const componentRef = useRef<HTMLDivElement>(null);
    const searchParams = useSearchParams();
    const addressId = searchParams.get('addressId') || '';
    const [editValue, setEditValue] = useState(getValue(year, item.key));

    useEffect(() => {
        setIsMounted(true);
    }, []);

    // Get the current source for this field to apply highlighting
    const currentSource = getCurrentSource(metadata, item.key);
    const highlightClass = getCellHighlighting(currentSource);

    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (componentRef.current && !componentRef.current.contains(event.target as Node)) {
                if (isEditing) setIsEditing(false);
                if (isCalculatorVisible) setIsCalculatorVisible(false);
            }
        }
        
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [isEditing, isCalculatorVisible]);

    const handleCalculatorClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        setIsCalculatorVisible(!isCalculatorVisible);
    };

    const handleSetCalculatorValue = (value: number) => {
        handleInputChange(year, item.key, value.toString(), 'human');
    };

    // Get NOI value for debt service calculator
    const getCurrentNOI = (): number => {
        const noiValue = getValue(year, 'net_operating_income');
        return parseFloat(noiValue) || 0;
    };

    // Get the display value - use calculated value for items with sub-items or section totals
    const getDisplayValue = () => {
        if (item.key === 'net_operating_income') {
            // For NOI, use the dedicated calculateNOI function
            return calculateNOI(year);
        }
        if (item.isSectionTotal) {
            // For section totals, use the calculateSectionTotal function
            return item.negative ? -Math.abs(calculateSectionTotal(year, section)) : calculateSectionTotal(year, section);
        }
        if (item.isCalculated && item.subItems && item.subItems.length > 0) {
            return item.negative ? -Math.abs(calculateParentValue(item, year, getValue)) : calculateParentValue(item, year, getValue);
        }
        return item.negative ? -Math.abs(parseFloat(getValue(year, item.key) || '0')) : parseFloat(getValue(year, item.key) || '0');
    };

    const handleEditSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        // Remove currency formatting and get raw number
        const rawValue = editValue.replace(/[^0-9.-]+/g, '');
        const numValue = parseFloat(rawValue);
        
        if (!isNaN(numValue)) {
            let finalValue = numValue;
            
            if (item.negative) {
                // For vacancy and credit loss, ensure value is negative
                finalValue = -Math.abs(numValue);
            } else {
                // For other fields like other income, keep as positive
                finalValue = Math.abs(numValue);
            }
            
            // Update parent component state with the new value
            handleInputChange(year, item.key, finalValue.toString(), 'human');
        }
        
        // Always close the edit mode
        setIsEditing(false);
    };

    const handleEditClick = () => {
        const currentValue = getValue(year, item.key);
        if (currentValue) {
            const numValue = parseFloat(currentValue.toString().replace(/[^0-9.-]+/g, ''));
            if (!isNaN(numValue)) {
                // For negative fields (vacancy/credit loss), show positive value in input
                setEditValue(Math.abs(numValue).toString());
            } else {
                setEditValue('0');
            }
        } else {
            setEditValue('0');
        }
        setIsEditing(true);
    };

    const renderCalculationTools = () => {
        const aiCalculationFields = ['other_income', 'vacancy_loss', 'credit_loss'];
        const calculatorFields = ['other_income', 'vacancy_loss', 'credit_loss', 'rental_income', 'annual_debt_service'];
        
        return (
            <div className="flex items-center space-x-2">
                {/* Edit Pencil - Show for all non-total, non-calculated fields EXCEPT AI calculation fields */}
                {!item.edit?.isAICalculation && (item.isSectionTotal || (item.edit?.isCalculated)) ? <></> : (
                    <button
                        onClick={handleEditClick}
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                        <FontAwesomeIcon
                            icon={faPen}
                            className="h-3 w-3 text-gray-400 hover:text-indigo-500"
                        />
                    </button>
                )}

                {/* Calculator - Show for specific fields */}
                {calculatorFields.includes(item.key) && (
                    <div className="relative opacity-0 group-hover:opacity-100 transition-opacity">
                        <FontAwesomeIcon 
                            icon={faCalculator} 
                            className="text-gray-600 h-3 w-3 hover:text-indigo-500 cursor-pointer" 
                            onClick={handleCalculatorClick}
                        />
                        {isCalculatorVisible && isMounted && item.key === 'rental_income' && createPortal(
                            <WorkspaceDetailFinancialsItemCalculator
                                units={units}
                                setUnits={setUnits}
                                setIsCalculatorVisible={setIsCalculatorVisible}
                                handleSetCalculatorValue={handleSetCalculatorValue}
                                handleInputChange={handleInputChange}
                                handleBatchInputChange={handleBatchInputChange}
                                year={year}
                                itemKey={item.key}
                                grossScheduledIncome={parseFloat(getValue(year, 'rental_income')) || 0}
                                getValue={getValue}
                            />,
                            document.body
                        )}
                        {isCalculatorVisible && isMounted && item.key === 'annual_debt_service' && createPortal(
                            <WorkspaceDetailFinancialsItemDebtCalculator
                                noi={getCurrentNOI()}
                                setIsCalculatorVisible={setIsCalculatorVisible}
                                handleSetCalculatorValue={handleSetCalculatorValue}
                            />,
                            document.body
                        )}
                        {isCalculatorVisible && isMounted && ['other_income', 'vacancy_loss', 'credit_loss'].includes(item.key) && createPortal(
                            <WorkspaceDetailFinancialsItemCalculator
                                units={units}
                                setUnits={setUnits}
                                setIsCalculatorVisible={setIsCalculatorVisible}
                                handleSetCalculatorValue={handleSetCalculatorValue}
                                handleInputChange={handleInputChange}
                                handleBatchInputChange={handleBatchInputChange}
                                year={year}
                                itemKey={item.key}
                                grossScheduledIncome={parseFloat(getValue(year, 'rental_income')) || 0}
                                getValue={getValue}
                            />,
                            document.body
                        )}
                    </div>
                )}

                {/* AI Calculator - Show for specific fields */}
                {aiCalculationFields.includes(item.key) && (
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                        <WorkspaceDetailFinancialsItemAI
                            itemKey={item.key}
                            year={year}
                            value={getValue(year, item.key)}
                            onValueChange={(value) => handleInputChange(year, item.key, value.toString(), 'ai')}
                            isEditing={isEditing}
                            setIsEditing={setIsEditing}
                            overrideUserInput={true}
                            onLoadingChange={setIsAICalculating}
                        />
                    </div>
                )}
            </div>
        );
    };

    return (
        <div
            ref={componentRef}
            className={`relative group py-2 ${item.isSectionTotal ? 'font-semibold' : ''} ${highlightClass}`}
            onMouseLeave={() => setIsCalculatorVisible(false)}
        >
            <div className="flex items-center justify-between py-1 px-2 hover:bg-gray-50 rounded">
                <div className="flex items-center space-x-4">
                    <div className="text-sm">
                        {isEditing ? (
                            <form onSubmit={handleEditSubmit} className="inline-block">
                                <input
                                    type="text"
                                    value={editValue}
                                    onChange={(e) => {
                                        // Only allow numbers and decimal point
                                        const value = e.target.value.replace(/[^\d.]/g, '');
                                        setEditValue(value);
                                    }}
                                    onBlur={handleEditSubmit}
                                    autoFocus
                                    className="w-24 p-1 text-sm border border-indigo-500 rounded shadow-sm focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                    placeholder="0"
                                />
                            </form>
                        ) : isAICalculating ? (
                            <div className="flex items-center space-x-2">
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-500"></div>
                                <span className="text-gray-500 text-xs">Calculating...</span>
                            </div>
                        ) : (() => {
                            const formattedValue = formatValue(getDisplayValue(), item.format);
                            return formattedValue ? (
                                <span className={(isNegativeValue(getDisplayValue()) || item.negative) ? 'text-red-600' : ''}>
                                    {formattedValue}
                                </span>
                            ) : (
                                <span className="text-gray-400">--</span>
                            );
                        })()}
                    </div>
                    {renderCalculationTools()}
                </div>
            </div>
        </div>
    );
}