import { faMinus, faPlus, faPercent, faDollarSign, faCalculator, faSync } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState, useEffect } from "react";

interface Props {
    noi: number; // Net Operating Income
    setIsCalculatorVisible: React.Dispatch<React.SetStateAction<boolean>>;
    handleSetCalculatorValue: (value: number) => void;
}

interface FredRateData {
    rate: number;
    date: string;
    series_id: string;
    source: string;
}

export default function WorkspaceDetailFinancialsItemDebtCalculator({
    noi,
    setIsCalculatorVisible,
    handleSetCalculatorValue
}: Props) {
    const [capRate, setCapRate] = useState(5.0); // Default 5% cap rate
    const [downPaymentPercent, setDownPaymentPercent] = useState(40.0); // Default 40% down payment
    const [interestRate, setInterestRate] = useState(7.5); // Default interest rate
    const [loanTermYears, setLoanTermYears] = useState(30); // Default 30-year loan
    const [isLoadingRate, setIsLoadingRate] = useState(false);
    const [fredRateData, setFredRateData] = useState<FredRateData | null>(null);
    const [position, setPosition] = useState({ top: 0, left: 0 });

    useEffect(() => {
        // Position calculator in a safe area of the viewport
        setPosition({
            top: Math.max(100, window.innerHeight / 6),
            left: Math.max(16, (window.innerWidth - 520) / 2)
        });
    }, []);

    // Calculated values
    const valuation = noi && capRate ? (noi * 100) / capRate : 0;
    const downPaymentAmount = valuation * (downPaymentPercent / 100);
    const loanAmount = valuation - downPaymentAmount;
    
    // Monthly payment calculation using standard mortgage formula
    const monthlyRate = interestRate / 100 / 12;
    const numPayments = loanTermYears * 12;
    const monthlyPayment = loanAmount && monthlyRate ? 
        loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
        (Math.pow(1 + monthlyRate, numPayments) - 1) : 0;
    const annualDebtService = monthlyPayment * 12;

    // Fetch current mortgage rates from FRED API
    const fetchFredRates = async () => {
        setIsLoadingRate(true);
        try {
            const response = await fetch('/api/fred-rates');
            const data = await response.json();
            
            if (data.error) {
                console.warn('FRED API error, using fallback rate:', data.fallbackRate);
                setInterestRate(data.fallbackRate || 7.5);
            } else {
                setFredRateData(data);
                setInterestRate(data.rate);
            }
        } catch (error) {
            console.error('Error fetching FRED rates:', error);
            // Keep current rate if fetch fails
        } finally {
            setIsLoadingRate(false);
        }
    };

    // Fetch rates on component mount
    useEffect(() => {
        fetchFredRates();
    }, []);

    const handleCapRateChange = (value: number) => {
        setCapRate(Math.max(0.1, Math.min(20, value))); // Cap between 0.1% and 20%
    };

    const handleDownPaymentChange = (value: number) => {
        setDownPaymentPercent(Math.max(0, Math.min(100, value))); // Cap between 0% and 100%
    };

    const handleInterestRateChange = (value: number) => {
        setInterestRate(Math.max(0.1, Math.min(30, value))); // Cap between 0.1% and 30%
    };

    const handleLoanTermChange = (value: number) => {
        setLoanTermYears(Math.max(1, Math.min(50, value))); // Cap between 1 and 50 years
    };

    const handleApplyDebtService = () => {
        if (annualDebtService > 0) {
            handleSetCalculatorValue(annualDebtService);
            setIsCalculatorVisible(false);
            
            // Note: The parent component will handle auto-recalculation of dependent fields
            // like DSCR, cash flow before taxes, etc. when handleInputChange is called
        }
    };

    return (
        <div
            className="fixed border border-gray-300 rounded-md bg-white shadow-lg z-[99999] p-4 w-[520px] max-w-[90vw] max-h-[80vh] flex flex-col"
            style={{
                top: `${position.top}px`,
                left: `${position.left}px`
            }}
        >
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-semibold text-gray-900">Debt Service Calculator</h3>
                <button
                    onClick={fetchFredRates}
                    disabled={isLoadingRate}
                    className="inline-flex items-center px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50"
                >
                    <FontAwesomeIcon 
                        icon={faSync} 
                        className={`mr-1 h-3 w-3 ${isLoadingRate ? 'animate-spin' : ''}`} 
                    />
                    {isLoadingRate ? 'Loading...' : 'Update Rate'}
                </button>
            </div>

            <div className="space-y-4 overflow-y-auto flex-1">
                {/* NOI Display */}
                <div className="bg-gray-50 p-3 rounded-md">
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                        Net Operating Income (NOI)
                    </label>
                    <div className="text-lg font-semibold text-gray-900">
                        ${noi?.toLocaleString('en-US') || '0'}
                    </div>
                </div>

                {/* Cap Rate */}
                <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                        Cap Rate (%)
                    </label>
                    <div className="flex items-center space-x-2">
                        <button 
                            onClick={() => handleCapRateChange(capRate - 0.1)}
                            className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-indigo-600 hover:bg-gray-100 rounded"
                        >
                            <FontAwesomeIcon icon={faMinus} className="h-3 w-3" />
                        </button>
                        <input 
                            type="number" 
                            step="0.1"
                            min="0.1"
                            max="20"
                            className="flex-1 border border-gray-300 rounded-md p-2 text-sm text-center"
                            value={capRate}
                            onChange={(e) => handleCapRateChange(parseFloat(e.target.value) || 0)}
                        />
                        <button 
                            onClick={() => handleCapRateChange(capRate + 0.1)}
                            className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-indigo-600 hover:bg-gray-100 rounded"
                        >
                            <FontAwesomeIcon icon={faPlus} className="h-3 w-3" />
                        </button>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                        Property Valuation: ${valuation.toLocaleString('en-US')}
                    </div>
                </div>

                {/* Down Payment */}
                <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                        Down Payment (%)
                    </label>
                    <div className="flex items-center space-x-2">
                        <button 
                            onClick={() => handleDownPaymentChange(downPaymentPercent - 5)}
                            className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-indigo-600 hover:bg-gray-100 rounded"
                        >
                            <FontAwesomeIcon icon={faMinus} className="h-3 w-3" />
                        </button>
                        <input 
                            type="number" 
                            step="1"
                            min="0"
                            max="100"
                            className="flex-1 border border-gray-300 rounded-md p-2 text-sm text-center"
                            value={downPaymentPercent}
                            onChange={(e) => handleDownPaymentChange(parseFloat(e.target.value) || 0)}
                        />
                        <button 
                            onClick={() => handleDownPaymentChange(downPaymentPercent + 5)}
                            className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-indigo-600 hover:bg-gray-100 rounded"
                        >
                            <FontAwesomeIcon icon={faPlus} className="h-3 w-3" />
                        </button>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                        Down Payment Amount: ${downPaymentAmount.toLocaleString('en-US')}
                    </div>
                </div>

                {/* Interest Rate */}
                <div>
                    <div className="flex items-center justify-between mb-1">
                        <label className="text-xs font-medium text-gray-700">
                            Interest Rate (%)
                        </label>
                        {fredRateData && (
                            <span className="text-xs text-green-600">
                                FRED: {fredRateData.rate}% ({fredRateData.date})
                            </span>
                        )}
                    </div>
                    <div className="flex items-center space-x-2">
                        <button 
                            onClick={() => handleInterestRateChange(interestRate - 0.1)}
                            className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-indigo-600 hover:bg-gray-100 rounded"
                        >
                            <FontAwesomeIcon icon={faMinus} className="h-3 w-3" />
                        </button>
                        <input 
                            type="number" 
                            step="0.1"
                            min="0.1"
                            max="30"
                            className="flex-1 border border-gray-300 rounded-md p-2 text-sm text-center"
                            value={interestRate}
                            onChange={(e) => handleInterestRateChange(parseFloat(e.target.value) || 0)}
                        />
                        <button 
                            onClick={() => handleInterestRateChange(interestRate + 0.1)}
                            className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-indigo-600 hover:bg-gray-100 rounded"
                        >
                            <FontAwesomeIcon icon={faPlus} className="h-3 w-3" />
                        </button>
                    </div>
                </div>

                {/* Loan Term */}
                <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                        Loan Term (Years)
                    </label>
                    <div className="flex items-center space-x-2">
                        <button 
                            onClick={() => handleLoanTermChange(loanTermYears - 5)}
                            className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-indigo-600 hover:bg-gray-100 rounded"
                        >
                            <FontAwesomeIcon icon={faMinus} className="h-3 w-3" />
                        </button>
                        <input 
                            type="number" 
                            step="1"
                            min="1"
                            max="50"
                            className="flex-1 border border-gray-300 rounded-md p-2 text-sm text-center"
                            value={loanTermYears}
                            onChange={(e) => handleLoanTermChange(parseInt(e.target.value) || 1)}
                        />
                        <button 
                            onClick={() => handleLoanTermChange(loanTermYears + 5)}
                            className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-indigo-600 hover:bg-gray-100 rounded"
                        >
                            <FontAwesomeIcon icon={faPlus} className="h-3 w-3" />
                        </button>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                        Loan Amount: ${loanAmount.toLocaleString('en-US')}
                    </div>
                </div>

                {/* Results */}
                <div className="bg-indigo-50 p-4 rounded-md border border-indigo-200">
                    <div className="space-y-2">
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-700">Monthly Payment:</span>
                            <span className="text-sm font-semibold text-gray-900">
                                ${monthlyPayment.toLocaleString('en-US')}
                            </span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm font-medium text-indigo-700">Annual Debt Service:</span>
                            <span className="text-lg font-bold text-indigo-700">
                                ${annualDebtService.toLocaleString('en-US')}
                            </span>
                        </div>
                        {noi > 0 && (
                            <div className="flex justify-between items-center">
                                <span className="text-xs text-gray-600">DSCR:</span>
                                <span className="text-xs font-medium text-gray-900">
                                    {(noi / annualDebtService).toFixed(2)}x
                                </span>
                            </div>
                        )}
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-between items-center pt-3 border-t border-gray-200">
                    <button 
                        onClick={() => setIsCalculatorVisible(false)}
                        className="px-4 py-2 text-sm border border-gray-300 rounded hover:bg-gray-100 text-gray-700"
                    >
                        Cancel
                    </button>
                    <button 
                        onClick={handleApplyDebtService}
                        className="px-4 py-2 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700"
                    >
                        Apply Annual Debt Service
                    </button>
                </div>
            </div>
        </div>
    );
} 