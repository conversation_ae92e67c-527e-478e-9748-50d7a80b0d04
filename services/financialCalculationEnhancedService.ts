import { getPropertyFinancialsUnits } from "@/actions/propertyFinancials";

interface Unit {
    unit: string;
    rent: number;
    [key: string]: any;
}

interface FinancialData {
    [key: string]: {
        [year: number]: number;
    };
}

interface CalculationProgress {
    current: string;
    completed: string[];
    total: number;
    percentage: number;
}

export class FinancialCalculationService {
    private progressCallback?: (progress: CalculationProgress) => void;
    
    constructor(progressCallback?: (progress: CalculationProgress) => void) {
        this.progressCallback = progressCallback;
    }

    private updateProgress(current: string, completed: string[], total: number) {
        if (this.progressCallback) {
            this.progressCallback({
                current,
                completed,
                total,
                percentage: Math.round((completed.length / total) * 100)
            });
        }
    }

    async recalculateAllFinancials(
        addressId: string | null,
        portfolioId: string,
        currentData: FinancialData
    ): Promise<FinancialData> {
        const calculationSteps = [
            'Calculating Income Projections',
            'Processing Expense Projections', 
            'Computing Net Operating Income',
            'Analyzing Debt Service',
            'Determining Cash Flow',
            'Calculating Valuation Metrics',
            'Finalizing Portfolio Metrics'
        ];
        
        let completed: string[] = [];
        let calculatedData = { ...currentData };

        // Step 1: Calculate Income Projections
        this.updateProgress('Calculating Income Projections...', completed, calculationSteps.length);
        calculatedData = await this.calculateIncomeProjections(calculatedData);
        completed.push('Income Projections');

        // Step 2: Calculate Expense Projections
        this.updateProgress('Processing Expense Projections...', completed, calculationSteps.length);
        calculatedData = await this.calculateExpenseProjections(calculatedData);
        completed.push('Expense Projections');

        // Step 3: Calculate Net Operating Income
        this.updateProgress('Computing Net Operating Income...', completed, calculationSteps.length);
        calculatedData = await this.calculateNetOperatingIncome(calculatedData);
        completed.push('Net Operating Income');

        // Step 4: Calculate Debt Service
        this.updateProgress('Analyzing Debt Service...', completed, calculationSteps.length);
        calculatedData = await this.calculateDebtService(calculatedData);
        completed.push('Debt Service');

        // Step 5: Calculate Cash Flow
        this.updateProgress('Determining Cash Flow...', completed, calculationSteps.length);
        calculatedData = await this.calculateCashFlow(calculatedData);
        completed.push('Cash Flow');

        // Step 6: Calculate Valuation Metrics
        this.updateProgress('Calculating Valuation Metrics...', completed, calculationSteps.length);
        calculatedData = await this.calculateValuationMetrics(calculatedData, addressId);
        completed.push('Valuation Metrics');

        // Step 7: Calculate Portfolio Metrics
        this.updateProgress('Finalizing Portfolio Metrics...', completed, calculationSteps.length);
        calculatedData = await this.calculatePortfolioMetrics(calculatedData, portfolioId);
        completed.push('Portfolio Metrics');

        this.updateProgress('Calculations Complete!', completed, calculationSteps.length);
        
        return calculatedData;
    }

    private async calculateIncomeProjections(data: FinancialData): Promise<FinancialData> {
        const result = { ...data };

        for (let year = 1; year <= 10; year++) {
            // Gross Scheduled Income = Long Term Rental + Short Term Rental
            const longTermRental = result.long_term_rental?.[year] || 0;
            const shortTermRental = result.short_term_rental?.[year] || 0;
            
            // If no sub-rental data, use existing rental_income value
            let grossScheduledIncome = longTermRental + shortTermRental;
            if (grossScheduledIncome === 0 && result.rental_income?.[year]) {
                grossScheduledIncome = result.rental_income[year];
            }
            
            // If still no income data and this is Year 1, provide a minimal example for calculation
            if (grossScheduledIncome === 0 && year === 1) {
                grossScheduledIncome = 5000 * 12; // Example: $5000/month = $60,000/year
                
                // Also set the sub-items for consistency
                if (!result.long_term_rental) result.long_term_rental = {};
                result.long_term_rental[year] = grossScheduledIncome;
            }
            
            // For years 2-10, apply inflation if Year 1 has data but subsequent years don't
            if (year > 1 && grossScheduledIncome === 0 && result.rental_income?.[1]) {
                const year1Income = result.rental_income[1];
                const inflationRate = 0.03; // 3% annual inflation
                grossScheduledIncome = Math.round(year1Income * Math.pow(1 + inflationRate, year - 1));
            }
            
            if (!result.rental_income) result.rental_income = {};
            result.rental_income[year] = grossScheduledIncome;

            // Vacancy Loss = Gross Scheduled Income × Vacancy Rate %
            if (!result.vacancy_loss) result.vacancy_loss = {};
            const vacancyRate = 0.05; // 5% default
            result.vacancy_loss[year] = -(grossScheduledIncome * vacancyRate);

            // Credit Loss = Gross Scheduled Income × Credit Loss Rate %
            if (!result.credit_loss) result.credit_loss = {};
            const creditLossRate = 0.02; // 2% default
            result.credit_loss[year] = -(grossScheduledIncome * creditLossRate);

            // Effective Gross Income = Gross Scheduled Income - Vacancy Loss - Credit Loss
            if (!result.effective_gross_income) result.effective_gross_income = {};
            const otherIncome = result.other_income?.[year] || 0;
            const vacancyLoss = result.vacancy_loss[year] || 0;
            const creditLoss = result.credit_loss[year] || 0;
            
            result.effective_gross_income[year] = grossScheduledIncome + otherIncome + vacancyLoss + creditLoss;
        }

        return result;
    }

    private async calculateExpenseProjections(data: FinancialData): Promise<FinancialData> {
        const result = { ...data };

        for (let year = 1; year <= 10; year++) {
            // If no expense data exists for Year 1, provide example defaults
            if (year === 1) {
                const hasAnyExpenseData = result.property_tax?.[year] || result.insurance?.[year] || 
                    result.utilities?.[year] || result.repairs?.[year] || result.maintenance?.[year];
                
                if (!hasAnyExpenseData) {
                    // Add some reasonable defaults for calculation
                    if (!result.property_tax) result.property_tax = {};
                    if (!result.insurance) result.insurance = {};
                    if (!result.utilities) result.utilities = {};
                    if (!result.repairs) result.repairs = {};
                    if (!result.maintenance) result.maintenance = {};
                    
                    result.property_tax[year] = 8000; // $8k property tax
                    result.insurance[year] = 1200; // $1.2k insurance
                    result.utilities[year] = 2400; // $200/month utilities
                    result.repairs[year] = 3000; // $3k repairs
                    result.maintenance[year] = 2000; // $2k maintenance
                }
            }
            
            // For years 2-10, apply inflation if Year 1 has data but subsequent years don't
            if (year > 1) {
                const inflationRate = 0.03; // 3% annual inflation
                ['property_tax', 'insurance', 'utilities', 'repairs', 'maintenance'].forEach(expenseType => {
                    if (!result[expenseType]) result[expenseType] = {};
                    if (!result[expenseType][year] && result[expenseType][1]) {
                        const year1Value = result[expenseType][1];
                        result[expenseType][year] = Math.round(year1Value * Math.pow(1 + inflationRate, year - 1));
                    }
                });
            }

            // Professional Fees = Sum of all professional fee subcategories
            if (!result.professional_fees) result.professional_fees = {};
            const managementFees = result.management_fees?.[year] || 0;
            const leasingFees = result.leasing_fees?.[year] || 0;
            const legalFees = result.legal_fees?.[year] || 0;
            const accountingFees = result.accounting_fees?.[year] || 0;
            const engineeringFees = result.engineering_fees?.[year] || 0;
            const marketingFees = result.marketing_fees?.[year] || 0;
            const consultingFees = result.consulting_fees?.[year] || 0;
            
            result.professional_fees[year] = -(Math.abs(managementFees) + Math.abs(leasingFees) + 
                Math.abs(legalFees) + Math.abs(accountingFees) + Math.abs(engineeringFees) + 
                Math.abs(marketingFees) + Math.abs(consultingFees));

            // Total Operating Expenses = Sum of all expense categories
            if (!result.total_operating_expenses) result.total_operating_expenses = {};
            const propertyTax = Math.abs(result.property_tax?.[year] || 0);
            const insurance = Math.abs(result.insurance?.[year] || 0);
            const repairs = Math.abs(result.repairs?.[year] || 0);
            const maintenance = Math.abs(result.maintenance?.[year] || 0);
            const professionalFees = Math.abs(result.professional_fees[year] || 0);
            const utilities = Math.abs(result.utilities?.[year] || 0);
            const services = Math.abs(result.services?.[year] || 0);
            const reserves = Math.abs(result.reserves?.[year] || 0);
            
            result.total_operating_expenses[year] = -(propertyTax + insurance + repairs + 
                maintenance + professionalFees + utilities + services + reserves);
        }

        return result;
    }

    private async calculateNetOperatingIncome(data: FinancialData): Promise<FinancialData> {
        const result = { ...data };

        for (let year = 1; year <= 5; year++) {
            // Net Operating Income = Effective Gross Income - Total Operating Expenses
            if (!result.net_operating_income) result.net_operating_income = {};
            const effectiveGrossIncome = result.effective_gross_income?.[year] || 0;
            const totalOperatingExpenses = result.total_operating_expenses?.[year] || 0;
            
            result.net_operating_income[year] = effectiveGrossIncome + totalOperatingExpenses; // totalOperatingExpenses is negative
        }

        return result;
    }

    private async calculateDebtService(data: FinancialData): Promise<FinancialData> {
        const result = { ...data };

        for (let year = 1; year <= 5; year++) {
            // DSCR = Net Operating Income ÷ Annual Debt Service
            if (!result.dscr) result.dscr = {};
            const noi = result.net_operating_income?.[year] || 0;
            const annualDebtService = Math.abs(result.annual_debt_service?.[year] || 0);
            
            if (annualDebtService > 0) {
                result.dscr[year] = noi / annualDebtService;
            } else {
                result.dscr[year] = 0;
            }
        }

        return result;
    }

    private async calculateCashFlow(data: FinancialData): Promise<FinancialData> {
        const result = { ...data };

        for (let year = 1; year <= 10; year++) {
            // Cash Flow Before Taxes = Net Operating Income - Annual Debt Service
            if (!result.cash_flow_before_taxes) result.cash_flow_before_taxes = {};
            const noi = result.net_operating_income?.[year] || 0;
            const annualDebtService = result.annual_debt_service?.[year] || 0;
            
            result.cash_flow_before_taxes[year] = noi + annualDebtService; // annualDebtService is negative

            // Cash Flow After Taxes (simplified)
            if (!result.cash_flow_after_taxes) result.cash_flow_after_taxes = {};
            const cashFlowBeforeTaxes = result.cash_flow_before_taxes[year];
            const estimatedTaxRate = 0.22; // 22% tax rate
            const taxLiability = Math.max(0, cashFlowBeforeTaxes * estimatedTaxRate);
            result.cash_flow_after_taxes[year] = cashFlowBeforeTaxes - taxLiability;

            // Cumulative Cash Flow = Previous Cumulative + Current Year Cash Flow
            if (!result.cumulative_cash_flow) result.cumulative_cash_flow = {};
            const previousCumulative = year > 1 ? (result.cumulative_cash_flow[year - 1] || 0) : 0;
            result.cumulative_cash_flow[year] = previousCumulative + result.cash_flow_after_taxes[year];
        }

        return result;
    }

    private async calculateValuationMetrics(data: FinancialData, addressId: string | null): Promise<FinancialData> {
        const result = { ...data };

        // Get property value from database or use default
        const propertyValue = addressId ? await this.getPropertyValue(addressId) : 500000;
        const initialCashInvestment = addressId ? await this.getInitialCashInvestment(addressId) : 100000;

        for (let year = 1; year <= 10; year++) {
            // Cap Rate = Net Operating Income ÷ Property Value × 100
            if (!result.cap_rate) result.cap_rate = {};
            const noi = result.net_operating_income?.[year] || 0;
            if (propertyValue > 0) {
                result.cap_rate[year] = (noi / propertyValue) * 100;
            } else {
                result.cap_rate[year] = 0;
            }

            // Gross Rent Multiplier = Property Value ÷ Gross Annual Rental Income
            if (!result.gross_rent_multiplier) result.gross_rent_multiplier = {};
            const grossRentalIncome = result.rental_income?.[year] || 0;
            if (grossRentalIncome > 0) {
                result.gross_rent_multiplier[year] = propertyValue / grossRentalIncome;
            } else {
                result.gross_rent_multiplier[year] = 0;
            }

            // Cash-on-Cash Return = Annual Cash Flow ÷ Initial Cash Investment × 100
            if (!result.cash_on_cash_return) result.cash_on_cash_return = {};
            const cashFlow = result.cash_flow_after_taxes?.[year] || 0;
            if (initialCashInvestment > 0) {
                result.cash_on_cash_return[year] = (cashFlow / initialCashInvestment) * 100;
            } else {
                result.cash_on_cash_return[year] = 0;
            }

            // Equity Multiple = (Cumulative Cash Flow + Current Property Value) ÷ Initial Equity
            if (!result.equity_multiple) result.equity_multiple = {};
            const cumulativeCashFlow = result.cumulative_cash_flow?.[year] || 0;
            if (initialCashInvestment > 0) {
                result.equity_multiple[year] = (cumulativeCashFlow + propertyValue) / initialCashInvestment;
            } else {
                result.equity_multiple[year] = 0;
            }
        }

        return result;
    }

    private async calculatePortfolioMetrics(data: FinancialData, portfolioId: string): Promise<FinancialData> {
        const result = { ...data };

        for (let year = 1; year <= 10; year++) {
            if (!result.total_acquisition_cost) result.total_acquisition_cost = {};
            result.total_acquisition_cost[year] = await this.getTotalAcquisitionCost(portfolioId);

            if (!result.aggregated_noi) result.aggregated_noi = {};
            result.aggregated_noi[year] = await this.getAggregatedNOI(portfolioId, year);

            if (!result.blended_cap_rate) result.blended_cap_rate = {};
            result.blended_cap_rate[year] = await this.getBlendedCapRate(portfolioId, year);

            if (!result.portfolio_irr) result.portfolio_irr = {};
            result.portfolio_irr[year] = 12.5; // Placeholder IRR
        }

        return result;
    }

    // Helper methods
    private async getPropertyValue(addressId: string): Promise<number> {
        try {
            const { createClient } = require('@/utils/supabase/server');
            const supabase = await createClient();
            
            const { data } = await supabase
                .from('prop_details')
                .select('purchase_price, current_value')
                .eq('address_id', addressId)
                .single();
                
            return data?.current_value || data?.purchase_price || 500000;
        } catch {
            return 500000;
        }
    }

    private async getInitialCashInvestment(addressId: string): Promise<number> {
        return 100000; // Placeholder
    }

    private async getTotalAcquisitionCost(portfolioId: string): Promise<number> {
        return 1000000; // Placeholder
    }

    private async getAggregatedNOI(portfolioId: string, year: number): Promise<number> {
        return 50000; // Placeholder
    }

    private async getBlendedCapRate(portfolioId: string, year: number): Promise<number> {
        return 6.5; // Placeholder
    }

    // Calculate portfolio completion percentage
    async calculatePortfolioCompletion(portfolioId: string): Promise<number> {
        try {
            const { createClient } = require('@/utils/supabase/server');
            const supabase = await createClient();
            
            // Get all properties in portfolio
            const { data: properties } = await supabase
                .from('prop')
                .select('id')
                .eq('portfolio_id', portfolioId);
                
            if (!properties || properties.length === 0) return 0;
            
            const totalProperties = properties.length;
            let totalFields = 0;
            let filledFields = 0;
            
            const requiredFields = [
                'rental_income', 'other_income', 'vacancy_loss', 'credit_loss',
                'property_tax', 'insurance', 'repairs', 'maintenance', 'professional_fees',
                'utilities', 'services', 'reserves', 'annual_debt_service'
            ];
            
            for (const property of properties) {
                // Get financial data for this property
                const { data: financialData } = await supabase
                    .from('prop_financials')
                    .select('*')
                    .eq('prop_id', property.id);
                    
                for (const field of requiredFields) {
                    for (let year = 1; year <= 10; year++) {
                        totalFields++;
                        
                        // Check if this year and field combination has data
                        const yearData = financialData?.find((d: any) => d.year === year);
                        if (yearData && yearData[field] !== null && yearData[field] !== undefined && yearData[field] !== 0) {
                            filledFields++;
                        }
                    }
                }
            }
            
            return totalFields > 0 ? Math.round((filledFields / totalFields) * 100) : 0;
        } catch (error) {
            console.error('Error calculating portfolio completion:', error);
            return 0;
        }
    }
}

// Legacy static class for backward compatibility
export class FinancialCalculationEnhancedService {
    static async getUnitsForProperty(addressId: string): Promise<Unit[]> {
        try {
            const units = await getPropertyFinancialsUnits(addressId);
            return units || [];
        } catch (error) {
            console.error('Error fetching units for property:', error);
            return [];
        }
    }

    static calculateTotalRentalIncome(units: Unit[]): number {
        return units.reduce((total, unit) => {
            const monthlyRent = unit.rent || 0;
            return total + (monthlyRent * 12);
        }, 0);
    }

    static applyPercentageToGrossIncome(grossIncome: number, percentage: number, isNegative: boolean = false): number {
        const amount = grossIncome * (percentage / 100);
        return isNegative ? -Math.abs(amount) : Math.abs(amount);
    }

    static formatCurrency(value: number): string {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(Math.abs(value));
    }

    static parseCurrency(value: string): number {
        return parseFloat(value.replace(/[^0-9.-]/g, '')) || 0;
    }

    static getDefaultRates() {
        return {
            other_income: 3, // 3%
            vacancy_loss: 5, // 5%
            credit_loss: 2   // 2%
        };
    }
} 