'use client'
import { dataroomEvents, getPortfolioDocuments, getPortfolioDocumentsTest, downloadAllPortfolioDocuments, toggleDocumentHidden } from '@/actions/dataRoomActions';
import { useParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import DocumentTree from '@/components/WorkspacePageDetail/DataRoom/DocumentTree';
import PDFViewer from '@/components/WorkspacePageDetail/DataRoom/PDFViewer';
import modalTriggerType from '@/constants/modalTriggerType';
import { useModal } from '@/context/ModalContext';
import Spinner from '@/components/UI/Spinner';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';
import pathName from '@/constants/pathName';
import WorkspaceDetailChat from '@/components/WorkspacePageDetail/WorkspaceDetailChat/WorkspaceDetailChat';
import { ChatActiveType } from '@/types/ChatActiveType';
import { createChat, getChatActive, getChatList } from '@/actions/chatActions';
import { getPortfolioOwner } from '@/actions/dataRoomActions';
import modalType from '@/constants/modalType';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDownload, faShare, faSpinner, faFolder, faChevronDown, faChevronRight, faFile, faMagicWandSparkles, faArrowLeft, faChevronLeft } from '@fortawesome/free-solid-svg-icons';
import { ChatListType } from '@/types/ChatListType';
import {documentGenerateSlides} from '@/actions/documentGenerateSlides'

interface DocumentNode {
  id: string;
  type: 'folder' | 'file';
  name: string;
  document_type?: string;
  children?: DocumentNode[];
  url?: string;
  created_at?: string;
  uploaded_at?: string;
  summary?: string;
  page_count?: number;
  preview_url?: string;
  property_id?: string;
  sub_type?: string;
  is_hidden?: boolean;
}

export default function DataRoomPage() {
  const {id} = useParams();
  const {isLoadingUserData,user, sharedList} = useAuth();
  const [documents, setDocuments] = useState<any>(null);
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<any>(null);
  const {showModal, updateModalData, updateModalTrigger, modalTrigger, modalData} = useModal();

  const [isChatExpanded, setIsChatExpanded] = useState(true)
  const [portfolioOwnerId, setPortfolioOwnerId] = useState<string | null>(null);
  const [sharedData, setSharedData] = useState<{ [key: string]: any } | null>(null);

  const [chatList, setChatList] = useState<ChatListType | null>(null)
  const [chatActive, setChatActive] = useState<ChatActiveType | null>(null)

  const [collapseAllTrigger, setCollapseAllTrigger] = useState(0);

  const [isLoadingRegeneratedDocumnet, setisLoadingRegeneratedDocumnet] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState({ 
    current: 0, 
    total: 0, 
    status: '', 
    isComplete: false, 
    isError: false 
  });
  const [isMobileTreeExpanded, setIsMobileTreeExpanded] = useState(false);
  const [isDesktopTreeCollapsed, setIsDesktopTreeCollapsed] = useState(false);

  const handleChatActive = (chat_id: string) => {
      setChatActive(chatList?.chats?.find(item => item?.id === chat_id) as ChatActiveType)
  }

  const handleCreateChat = async () => {
    try {
      const newChat = await createChat(id as string)
      const chatList = await getChatList(id as string)
      setChatList(chatList)
      setChatActive(chatList?.chats?.find(item => item?.id === newChat?.id) as ChatActiveType)
    } catch (error) {
      console.error('Error creating chat:', error);
      // Create a temporary chat object if API fails
      const tempChat: ChatActiveType = {
        id: `temp-${Date.now()}`,
        portfolio_id: id as string,
        created_by: user?.user?.id || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted_at: null,
        is_deleted: false,
        name: 'Chat'
      };
      setChatActive(tempChat);
      setChatList({ chats: [tempChat] });
    }
  }

  useEffect(() => {
    getPortfolioDocuments(id as string).then((res) => {
      setDocuments(res);
    });
    
    // Always load chat data for both shared and owned portfolios
    getChatList(id as string).then((data) => {
      setChatList(data)
      // Set the first chat as active, or create one if none exists
      if (data?.chats && data.chats.length > 0) {
        setChatActive(data.chats[0])
      } else {
        // If no chats exist, create one automatically
        handleCreateChat();
      }
    }).catch((error) => {
      console.log('Error loading chat list:', error);
      // If chat loading fails, create a fallback chat for shared view
      if (sharedData || !user?.user?.id) {
        const tempChat: ChatActiveType = {
          id: `temp-${Date.now()}`,
          portfolio_id: id as string,
          created_by: user?.user?.id || 'shared-user',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          deleted_at: null,
          is_deleted: false,
          name: 'Chat'
        };
        setChatActive(tempChat);
        setChatList({ chats: [tempChat] });
      } else {
        // Try to create a new chat for authenticated users
        handleCreateChat();
      }
    });
    
    getPortfolioOwner(id as string).then((data) => {
      console.log(data, 222)
      setPortfolioOwnerId(data?.id);
    });
    
  }, [id, user?.user?.id]);

  useEffect(() => {
    if(sharedList && sharedList.length > 0) {
      setSharedData(sharedList.find((item) => item.portfolioData.id === id) || null);
    }
  }, [sharedList,id]);

  // Ensure chat is expanded by default in shared view
  useEffect(() => {
    if (sharedData) {
      setIsChatExpanded(true);
      
      // If no chat is active yet in shared view, create a temporary one
      if (!chatActive) {
        const tempChat: ChatActiveType = {
          id: `temp-shared-${Date.now()}`,
          portfolio_id: id as string,
          created_by: user?.user?.id || 'shared-user',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          deleted_at: null,
          is_deleted: false,
          name: 'Chat'
        };
        setChatActive(tempChat);
        if (!chatList || chatList.chats.length === 0) {
          setChatList({ chats: [tempChat] });
        }
      }
    }
  }, [sharedData, chatActive, chatList, id, user?.user?.id]);

  const isEffectDevFetchDataroomEvents = useRef(false);
  useEffect(() => {
    if(user && !isEffectDevFetchDataroomEvents.current) {
      isEffectDevFetchDataroomEvents.current = true;
      dataroomEvents(user?.user?.id, 'view', id as string);
    }
  }, [user]);

  useEffect(() => {
    if (modalTrigger === modalTriggerType.deletePortfolioDocument) {
      getPortfolioDocumentsTest(id as string).then((res) => {
        setDocuments(res);
      });
      updateModalTrigger(null);
    }

    if(modalTrigger === modalTriggerType.deleteChat){
      getChatList(id as string).then((data) => {
          
          setChatList(data)

          if(modalData?.chatId === chatActive?.id && data?.chats?.length > 0){
              setChatActive(data?.chats[0])
          }
      })
      updateModalTrigger(null)
    }
  }, [modalTrigger]);

  const handleFileSelect = (url: string) => {
    setSelectedFile(url);
    
    if (documents) {
      const document = findDocumentByUrl(documents, url);
      setSelectedDocument(document);
    }
  };

  const handleRegeneratedDocumnet =  () => {
    if(!isLoadingRegeneratedDocumnet){
      setisLoadingRegeneratedDocumnet(true)
      documentGenerateSlides(id as string, user?.user?.id as string).then((data) => {
        setisLoadingRegeneratedDocumnet(false)
      })
    }
    
  }

  // Function to recursively find a document by URL
  const findDocumentByUrl = (node: any, url: string): any => {
    if (node.type === 'file' && node.url === url) {
      return node;
    }
    
    if (node.children) {
      for (const child of node.children) {
        const found = findDocumentByUrl(child, url);
        if (found) return found;
      }
    }
    
    return null;
  };

  const handleDownloadAll = async () => {
    if (!id || isDownloading) return;

    try {
      setIsDownloading(true);
      setDownloadProgress({ current: 0, total: 0, status: 'Preparing documents...', isComplete: false, isError: false });

      // Import JSZip dynamically
      const JSZip = (await import('jszip')).default;
      
      const result = await downloadAllPortfolioDocuments(id as string);
      
      if (!result.success) {
        throw new Error('Failed to get document list');
      }

      if (result.totalFiles === 0) {
        throw new Error('No documents found in this portfolio');
      }

      setDownloadProgress({ current: 0, total: result.totalFiles, status: 'Starting download...', isComplete: false, isError: false });

      // Create zip file
      const zip = new JSZip();
      let downloadedCount = 0;
      let failedCount = 0;

      // Download and add files to zip
      for (const file of result.files) {
        try {
          setDownloadProgress({ 
            current: downloadedCount, 
            total: result.totalFiles, 
            status: `Downloading: ${file.name.length > 30 ? file.name.substring(0, 27) + '...' : file.name}`,
            isComplete: false,
            isError: false
          });

          const response = await fetch(file.url);
          if (response.ok) {
            const blob = await response.blob();
            zip.file(file.path, blob);
            downloadedCount++;
          } else {
            failedCount++;
            console.warn(`Failed to download ${file.name}: HTTP ${response.status}`);
          }
          
          setDownloadProgress({ 
            current: downloadedCount, 
            total: result.totalFiles, 
            status: `Downloaded ${downloadedCount}/${result.totalFiles} files${failedCount > 0 ? ` (${failedCount} failed)` : ''}`,
            isComplete: false,
            isError: false
          });
        } catch (error) {
          failedCount++;
          console.warn(`Failed to download ${file.name}:`, error);
        }
      }

      if (downloadedCount === 0) {
        throw new Error('No files could be downloaded. Please check your connection and try again.');
      }

      // Generate and download zip
      setDownloadProgress({ 
        current: downloadedCount, 
        total: result.totalFiles, 
        status: 'Creating zip file...',
        isComplete: false,
        isError: false
      });

      const zipBlob = await zip.generateAsync({ 
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: { level: 6 }
      });
      
      const url = URL.createObjectURL(zipBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${result.portfolioName}_Documents.zip`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      const successMessage = failedCount > 0 
        ? `Downloaded ${downloadedCount} files successfully! ${failedCount} files failed to download.`
        : `Successfully downloaded all ${downloadedCount} files!`;
      
      setDownloadProgress({ 
        current: downloadedCount, 
        total: result.totalFiles, 
        status: successMessage,
        isComplete: true,
        isError: false
      });

    } catch (error) {
      console.error('Error downloading documents:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      setDownloadProgress({ 
        current: 0, 
        total: 0, 
        status: `Error: ${errorMessage}`,
        isComplete: false,
        isError: true
      });
      
      // Show user-friendly alert
      if (errorMessage.includes('No documents found')) {
        alert('This portfolio doesn\'t contain any documents to download.');
      } else if (errorMessage.includes('No files could be downloaded')) {
        alert('Unable to download files. Please check your internet connection and try again.');
      } else {
        alert('Failed to download documents. Please try again or contact support if the problem persists.');
      }
    } finally {
      setIsDownloading(false);
      setTimeout(() => {
        setDownloadProgress({ current: 0, total: 0, status: '', isComplete: false, isError: false });
      }, 4000); // Show final status for 4 seconds
    }
  };

  // Keyboard shortcut for toggling document tree
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
        event.preventDefault();
        setIsDesktopTreeCollapsed(!isDesktopTreeCollapsed);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isDesktopTreeCollapsed]);


  const handleCollapseAll = () => {
    setCollapseAllTrigger(prev => prev + 1);
  };

  // Added hide functionality and owner check
  const isOwner = !sharedData; // If not in shared view, user is the owner
  
  const refreshDocuments = () => {
    getPortfolioDocuments(id as string).then((res) => {
      setDocuments(res);
      
      // Keep the same file selected if it still exists
      if (selectedFile && res) {
        const document = findDocumentByUrl(res, selectedFile);
        setSelectedDocument(document);
      }
    });
  };

  const handleDocumentHide = async () => {
    if (selectedDocument && id) {
      try {
        await toggleDocumentHidden(id as string, selectedDocument.id);
        refreshDocuments(); // Refresh to get updated is_hidden status
      } catch (error) {
        console.error('Error toggling document visibility:', error);
      }
    }
  };

  return (
    <div className="px-4 py-4 mb-8 h-[calc(100vh-150px)]">
      <style jsx>{`
        @media screen and (max-width: 768px) and (orientation: landscape) {
          .mobile-landscape-fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 50;
            background: white;
            padding: 0;
            margin: 0;
          }
          
          .mobile-landscape-pdf {
            height: 100vh;
            width: 100vw;
          }
          
          .mobile-landscape-hide {
            display: none;
          }
          
          .mobile-landscape-back-btn {
            display: block !important;
          }
        }
        
        @media screen and (max-width: 768px) and (orientation: portrait) {
          .mobile-portrait-show {
            display: block;
          }
          
          .mobile-landscape-back-btn {
            display: none !important;
          }
        }
      `}</style>
      {documents ? (
        <>
            {/* Header with title and action buttons */}
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 gap-4 mobile-landscape-hide" style={{ marginRight: isChatExpanded && window.innerWidth >= 768 ? '410px' : '0' }}>
              <div className="flex-1">
                <h1 className="text-xl sm:text-2xl font-bold text-left">{documents?.portfolio_name}</h1>
              </div>
              <div className="flex flex-wrap items-center gap-2 sm:gap-3 relative z-10">
                  {/* Regenerate Slides Button - Only show if not in shared view */}
                  {!sharedData && (
                    <div>
                        <button
                          disabled={isLoadingRegeneratedDocumnet}
                          onClick={() => handleRegeneratedDocumnet()}
                          className={`cursor-pointer text-xs sm:text-sm px-3 sm:px-4 py-2 rounded-md transition-all duration-300 flex items-center gap-1 sm:gap-2 shadow-sm hover:shadow-md transform font-medium min-w-[120px] sm:min-w-[160px] justify-center ${
                            isLoadingRegeneratedDocumnet 
                              ? 'bg-gray-400 cursor-not-allowed' 
                              : 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white hover:scale-105'
                          }`}
                          title={isLoadingRegeneratedDocumnet ? 'Regenerating slides...' : 'Regenerate presentation slides'}
                        >
                          {isLoadingRegeneratedDocumnet ? (
                            <>
                              <FontAwesomeIcon icon={faSpinner} className="h-3 w-3 animate-spin" />
                              <span className="hidden sm:inline">Re-generating...</span>
                              <span className="sm:hidden">Generating...</span>
                            </>
                          ) : (
                            <>
                              <FontAwesomeIcon icon={faSpinner} className="h-3 w-3" />
                              <span className="hidden sm:inline">Re-generate Slides</span>
                              <span className="sm:hidden">Slides</span>
                            </>
                          )}
                        </button>
                    </div>
                  )}
                  
                  {/* Download All Button */}
                  {sharedData?.can_download && (
                    <div>
                      <button 
                        className={`cursor-pointer text-xs sm:text-sm px-3 sm:px-4 py-2 rounded-md transition-all duration-300 flex items-center gap-1 sm:gap-2 shadow-sm hover:shadow-md transform font-medium min-w-[100px] sm:min-w-[140px] justify-center ${
                          isDownloading 
                            ? 'bg-gray-400 cursor-not-allowed' 
                            : 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white hover:scale-105'
                        }`}
                        onClick={handleDownloadAll}
                        disabled={isDownloading}
                        title={isDownloading ? downloadProgress.status : 'Download all documents as ZIP'}
                      >
                        {isDownloading ? (
                          <>
                            <FontAwesomeIcon icon={faDownload} className="h-3 w-3 animate-spin" />
                            <span className="text-xs">
                              {downloadProgress.total > 0 ? `${downloadProgress.current}/${downloadProgress.total}` : 'Prep...'}
                            </span>
                          </>
                        ) : (
                          <>
                            <FontAwesomeIcon icon={faDownload} className="h-3 w-3" />
                            <span className="hidden sm:inline">Download All</span>
                            <span className="sm:hidden">Download</span>
                          </>
                        )}
                      </button>
                    </div>
                  )}

                  {/* Share Button */}
                  {sharedData?.can_share && (
                    <div>
                      <button 
                        className="cursor-pointer text-xs sm:text-sm px-3 sm:px-4 py-2 rounded-md transition-all duration-300 flex items-center gap-1 sm:gap-2 shadow-sm hover:shadow-md transform font-medium bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white hover:scale-105" 
                        onClick={() => {
                          showModal(modalType.portfolioShare)
                          updateModalData({
                              ownerId: portfolioOwnerId,
                              portfolioId: id as string,
                              senderId: user?.user?.id,
                              portfolioName: documents?.portfolio_name
                          })
                        }}
                      >
                        <FontAwesomeIcon icon={faShare} className="h-3 w-3" />
                        <span className="hidden sm:inline">Share</span>
                        <span className="sm:hidden">Share</span>
                      </button>
                    </div>
                  )}
              </div>
            </div>

            {/* Download Progress Modal */}
            {isDownloading && (
              <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
                <div className="bg-white rounded-xl shadow-2xl p-4 sm:p-6 max-w-md w-full mx-4 border border-gray-200">
                  <div className="flex items-center gap-3 mb-4">
                    <div className={`w-8 sm:w-10 h-8 sm:h-10 rounded-full flex items-center justify-center ${
                      downloadProgress.isError 
                        ? 'bg-gradient-to-r from-red-500 to-red-600' 
                        : downloadProgress.isComplete 
                          ? 'bg-gradient-to-r from-green-500 to-green-600' 
                          : 'bg-gradient-to-r from-green-500 to-green-600'
                    }`}>
                      {downloadProgress.isError ? (
                        <span className="text-white font-bold text-lg">✕</span>
                      ) : downloadProgress.isComplete ? (
                        <span className="text-white font-bold text-lg">✓</span>
                      ) : (
                        <FontAwesomeIcon icon={faDownload} className="h-4 sm:h-5 w-4 sm:w-5 text-white animate-pulse" />
                      )}
                    </div>
                    <div>
                      <h3 className={`text-base sm:text-lg font-semibold ${
                        downloadProgress.isError 
                          ? 'text-red-800' 
                          : downloadProgress.isComplete 
                            ? 'text-green-800' 
                            : 'text-green-800'
                      }`}>
                        {downloadProgress.isError 
                          ? 'Download Failed' 
                          : downloadProgress.isComplete 
                            ? 'Download Complete!' 
                            : 'Downloading Documents'}
                      </h3>
                      <p className={`text-xs sm:text-sm ${
                        downloadProgress.isError 
                          ? 'text-red-600' 
                          : downloadProgress.isComplete 
                            ? 'text-green-600' 
                            : 'text-green-600'
                      }`}>
                        {documents?.portfolio_name || 'Portfolio Documents'}
                      </p>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  {downloadProgress.total > 0 && !downloadProgress.isError && (
                    <div className="mb-4">
                      <div className="flex justify-between text-xs sm:text-sm text-gray-600 mb-1">
                        <span>Progress</span>
                        <span>{downloadProgress.current}/{downloadProgress.total}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${(downloadProgress.current / downloadProgress.total) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Status Message */}
                  <p className={`text-xs sm:text-sm ${
                    downloadProgress.isError 
                      ? 'text-red-600' 
                      : downloadProgress.isComplete 
                        ? 'text-green-600' 
                        : 'text-gray-600'
                  }`}>
                    {downloadProgress.status}
                  </p>

                  {/* Close button for completed/error states */}
                  {(downloadProgress.isComplete || downloadProgress.isError) && (
                    <div className="mt-4 flex justify-end">
                      <button
                        onClick={() => setIsDownloading(false)}
                        className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-200 text-sm"
                      >
                        Close
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Mobile Layout */}
            <div className="md:hidden flex flex-col h-full">
              {/* Mobile Document Tree - Collapsible */}
              <div className="bg-white rounded-lg shadow mb-4 mobile-landscape-hide">
                <button 
                  onClick={() => setIsMobileTreeExpanded(!isMobileTreeExpanded)}
                  className="w-full p-4 flex items-center justify-between text-left hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <div className="flex items-center gap-2">
                    <FontAwesomeIcon icon={faFolder} className="h-4 w-4 text-blue-500" />
                    <span className="font-medium text-gray-900">Documents</span>
                  </div>
                  <FontAwesomeIcon 
                    icon={isMobileTreeExpanded ? faChevronDown : faChevronRight} 
                    className="h-4 w-4 text-gray-400 transition-transform duration-200" 
                  />
                </button>
                {isMobileTreeExpanded && (
                  <div className="border-t border-gray-200 max-h-80 overflow-y-auto">
                    {/* Collapse All Button for Mobile */}
                    <div className="flex justify-end p-2 bg-gray-50 border-b border-gray-200">
                      <button
                        onClick={handleCollapseAll}
                        className="cursor-pointer flex items-center gap-2 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                        title="Collapse all folders"
                      >
                        Collapse All
                      </button>
                    </div>
                    <DocumentTree 
                      data={documents} 
                      onFileSelect={handleFileSelect} 
                      portfolioId={id as string} 
                      isSharedView={true} 
                      collapseAllTrigger={collapseAllTrigger} 
                      isOwner={isOwner} 
                      onDocumentUpdate={refreshDocuments} 
                    />
                  </div>
                )}
              </div>

              {/* Mobile PDF Viewer */}
              <div className={`flex-1 bg-white rounded-lg shadow mb-4 overflow-hidden ${selectedFile ? 'mobile-landscape-fullscreen' : ''}`}>
                {selectedFile ? (
                  <div className="h-full mobile-landscape-pdf relative">
                    {/* Landscape mode back button */}
                    <button
                      onClick={() => setSelectedFile(null)}
                      className="absolute top-4 left-4 z-50 bg-black/50 text-white rounded-full p-3 shadow-lg hover:bg-black/70 transition-all duration-200 md:hidden mobile-landscape-back-btn"
                    >
                      <FontAwesomeIcon icon={faArrowLeft} className="h-4 w-4" />
                    </button>
                    <PDFViewer 
                      url={selectedFile} 
                      pageCount={selectedDocument?.page_count} 
                      isOwner={isOwner} 
                      isHidden={selectedDocument?.is_hidden} 
                      onHideToggle={handleDocumentHide} 
                      documentName={selectedDocument?.name} 
                    />
                  </div>
                ) : (
                  <div className="h-full flex items-center justify-center text-gray-500 p-8">
                    <div className="text-center">
                      <FontAwesomeIcon icon={faFile} className="h-12 w-12 text-gray-300 mb-4" />
                      <p className="text-sm">Select a document to view</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Mobile Chat Toggle Button */}
              {(sharedData || chatActive) && (
                <div className="fixed bottom-4 right-4 z-40 mobile-landscape-hide">
                  <button
                    onClick={() => setIsChatExpanded(!isChatExpanded)}
                    className="bg-indigo-600 hover:bg-indigo-700 text-white rounded-full p-4 shadow-lg transition-all duration-200 hover:scale-105"
                  >
                    <FontAwesomeIcon icon={faMagicWandSparkles} className="h-5 w-5" />
                  </button>
                </div>
              )}
            </div>
            {/* Desktop Layout */}
            <div className="hidden md:flex gap-x-4 h-full">
              <div className="bg-white rounded-lg shadow p-4 flex-1" style={{ 
                                  marginRight: isChatExpanded ? '400px' : '0'
                              }}>
                <div className='flex gap-x-4 h-full'>
                  {/* Collapsible Document Tree */}
                  <div className={`transition-all duration-300 ease-in-out overflow-hidden ${
                    isDesktopTreeCollapsed ? 'w-0 min-w-0' : 'min-w-[250px] w-1/3'
                  }`}>
                    <div className="h-full rounded-lg shadow bg-gray-50/50 flex flex-col">
                      {/* Collapse All Button for Desktop */}
                      {!isDesktopTreeCollapsed && (
                        <div className="flex justify-end p-2 bg-white border-b border-gray-200 rounded-t-lg">
                          <button
                            onClick={handleCollapseAll}
                            className="cursor-pointer flex items-center gap-2 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                            title="Collapse all folders"
                          >
                            Collapse All
                          </button>
                        </div>
                      )}
                      <div className="flex-1 overflow-y-auto">
                      {documents && documents.children ? (
                        // If root has children, render them directly to avoid showing root folder icon
                        <div className="space-y-0">
                          {documents.children.map((child: DocumentNode, index: number) => (
                            <DocumentTree 
                              key={child.id} 
                              data={child} 
                              level={0}
                              isLast={index === documents.children.length - 1}
                              onFileSelect={handleFileSelect} 
                              portfolioId={id as string} 
                              selectedFile={selectedFile}
                              isSharedView={false}
                              showUploadButton={index === documents.children.length - 1}
                              collapseAllTrigger={collapseAllTrigger}
                              isOwner={isOwner}
                              onDocumentUpdate={refreshDocuments}
                            />
                          ))}
                        </div>
                      ) : (
                        // Fallback to normal rendering
                        <DocumentTree 
                          data={documents} 
                          onFileSelect={handleFileSelect} 
                          portfolioId={id as string} 
                          selectedFile={selectedFile}
                          isSharedView={false}
                          showUploadButton={true}
                          collapseAllTrigger={collapseAllTrigger}
                          isOwner={isOwner}
                          onDocumentUpdate={refreshDocuments}
                        />
                      )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Toggle Button */}
                  <div className="flex flex-col justify-center">
                    <button
                      onClick={() => setIsDesktopTreeCollapsed(!isDesktopTreeCollapsed)}
                      className="bg-white hover:bg-gray-50 border border-gray-200 hover:border-indigo-300 rounded-lg p-3 shadow-sm hover:shadow-md transition-all duration-200 group relative"
                      title={`${isDesktopTreeCollapsed ? 'Show' : 'Hide'} document tree (Ctrl/Cmd + B)`}
                    >
                      <FontAwesomeIcon 
                        icon={isDesktopTreeCollapsed ? faChevronRight : faChevronLeft} 
                        className="h-4 w-4 text-gray-500 group-hover:text-indigo-600 transition-colors duration-200" 
                      />
                      
                      {/* Subtle indicator when collapsed */}
                      {isDesktopTreeCollapsed && (
                        <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-indigo-400 to-indigo-600 rounded-full opacity-60"></div>
                      )}
                    </button>
                  </div>
                  
                  {/* PDF Viewer */}
                  <div className="flex-1 rounded-lg shadow overflow-hidden">
                    {selectedFile ? (
                      <PDFViewer 
                        url={selectedFile} 
                        pageCount={selectedDocument?.page_count} 
                        isOwner={isOwner} 
                        isHidden={selectedDocument?.is_hidden} 
                        onHideToggle={handleDocumentHide} 
                        documentName={selectedDocument?.name} 
                      />
                    ) : (
                      <div className="h-full flex items-center justify-center text-gray-500">
                        <div className="text-center">
                          <FontAwesomeIcon icon={faFile} className="h-16 w-16 text-gray-300 mb-4" />
                          <p className="text-lg font-medium mb-2">Select a document to view</p>
                          <p className="text-sm text-gray-400">
                            {isDesktopTreeCollapsed 
                              ? 'Click the arrow button to show the document tree' 
                              : 'Choose a file from the document tree on the left'
                            }
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Chat Component - Mobile Full Screen or Desktop Sidebar */}
            {(sharedData || chatActive) && (
              <div className={`${
                isChatExpanded 
                  ? 'md:mb-5 fixed inset-0 md:right-8 md:top-[80px] md:bottom-8 md:left-auto md:w-[360px] bg-white border-0 md:border md:border-gray-200 md:rounded-[20px] shadow-xl z-50 overflow-hidden drop-shadow-lg shadow-indigo-200/20' 
                  : 'hidden md:block absolute left-0 right-0 bottom-8 z-40'
              }`}>
                <WorkspaceDetailChat 
                  setIsChatExpanded={setIsChatExpanded} 
                  isChatExpanded={isChatExpanded} 
                  chatActive={chatActive} 
                  chatList={chatList} 
                  handleChatActive={handleChatActive} 
                  handleCreateChat={handleCreateChat} 
                  portfolioId={id as string} 
                />
              </div>
            )}
        </>        
      ) : (
        <div className="text-center py-8">
          <Spinner size="lg" text="Loading documents..." fullPage={false} />
        </div>
      )}
    </div>
  );
} 