import type { Metadata } from "next";
import { Inter} from "next/font/google";
import "./globals.css";
import Header from "@/components/Header/Header";
import Providers from "@/providers/Providers";
import ModalWrap from "@/components/Modals/ModalWrap";
import StatusNotifications from "@/components/UI/StatusNotifications";
import Footer from "@/components/Footer/Footer";
import { Suspense } from 'react';
// Initialize cron jobs on application startup
import "@/lib/startup";

const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',  
  weight: ['400', '500', '600', '700'],
});


export const metadata: Metadata = {
  title: 'Relm Pro | AI Real Estate Market Analysis',
  description: 'AI-powered real estate market analysis, property insights, and investment recommendations for real estate professionals.',
  keywords: 'NYC real estate, market analysis, property investment, real estate data, AI analytics',
  metadataBase: new URL('https://relm.ai'),
  alternates: {
    canonical: '/',
  },
  authors: [{ name: '<PERSON><PERSON>' }],
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://relm.ai',
    title: 'Relm Pro | AI Real Estate Market Analysis',
    description: 'AI-powered real estate market analysis, property insights, and investment recommendations for real estate professionals.',
    siteName: 'Relm Pro',
    images: [
      {
        url: '/images/relm-og.png',
        width: 1200,
        height: 630,
        alt: 'Relm Pro - AI Real Estate Analytics',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Relm Pro | NYC Real Estate Market Analysis',
    description: 'AI-powered NYC real estate market analysis, property insights, and investment recommendations.',
    images: ['/images/relm-og.png'],
  },
  robots: {
    index: true,
    follow: true,
  },
  icons: {
    icon: '/favicon.png',
  },
};

export default async function RootLayout({ children }: Readonly<{ children: React.ReactNode; }>) {
  //const supabase = await createClient()
  //const { data: { session } } = await supabase.auth.getSession()

  //console.log('session', session)

  return (
    <html lang="en">
      <head>
        <script
          src={`https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`}
          async
          defer
        />
      </head>
      <Suspense fallback={<div></div>}>
        <Providers>
          <body className={`${inter.className} antialiased  h-[calc(100vh-70px)]`} suppressHydrationWarning={true}>
            <div className="flex flex-col h-full justify-between">
              <Header />
              <div className="flex-1 bg-white transition-all duration-300 ease-in-out pt-[70px]">
                <div className="w-full mx-auto h-full">
                  {children}
                </div>
              </div>
              <ModalWrap />
              <StatusNotifications />
            </div>
            <Footer />
          </body>
        </Providers>
      </Suspense>
    </html>
  );
}
